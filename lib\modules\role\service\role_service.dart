import 'package:get/get.dart';
import 'package:rolio/common/models/ai_role.dart';
import 'package:rolio/common/utils/error_handler.dart';
import 'package:rolio/common/utils/logger.dart';
import 'package:rolio/modules/role/repository/role_repository.dart';
import 'package:rolio/modules/role/repository/recommend_repository.dart';

/// 角色服务类
/// 
/// 负责处理AI角色详情的业务逻辑
class RoleService extends GetxService {
  // 角色仓库
  final RoleRepository _repository;
  
  // 推荐仓库 - 用于获取基本角色信息
  final RecommendRepository _recommendRepository;
  
  // 加载状态
  final RxBool isLoading = false.obs;
  
  // 收藏操作状态
  final RxBool isFavoriteLoading = false.obs;
  
  // 当前角色
  final Rx<AiRole?> currentRole = Rx<AiRole?>(null);
  
  // 错误信息
  final RxString errorMessage = ''.obs;
  
  // 构造函数
  RoleService({
    RoleRepository? repository, 
    RecommendRepository? recommendRepository
  }) : _repository = repository ?? Get.find<RoleRepository>(),
       _recommendRepository = recommendRepository ?? Get.find<RecommendRepository>() {
    LogUtil.debug('RoleService 构造函数执行');
  }
  
  @override
  void onInit() {
    super.onInit();
    LogUtil.info('RoleService初始化');
  }
  
  /// 获取角色详情
  /// 
  /// 通过API获取指定角色的详细信息，包括收藏状态
  /// [roleId] 角色ID
  /// [forceRefresh] 是否强制刷新，不使用缓存
  /// 返回角色详情，如果不存在则返回null
  Future<AiRole?> getRoleDetail(int roleId, {bool forceRefresh = false}) async {
    try {
      isLoading.value = true;
      errorMessage.value = '';
      
      LogUtil.debug('获取角色详情，ID: $roleId，强制刷新: $forceRefresh');
      
      // 调用角色仓库获取角色详情
      final role = await _repository.getRoleDetail(roleId, forceRefresh: forceRefresh);
      
      // 更新当前角色
      if (role != null) {
        currentRole.value = role;
        LogUtil.debug('成功获取角色详情: ${role.name}');
        return role;
      }
      
      // 如果没有找到角色，尝试从推荐仓库获取基本信息
      LogUtil.warn('未找到角色详情，尝试从推荐仓库获取基本信息，ID: $roleId');
      final basicRole = await _recommendRepository.getById(roleId);
      
      if (basicRole != null) {
        currentRole.value = basicRole;
        LogUtil.debug('成功从推荐仓库获取基本角色信息: ${basicRole.name}');
        return basicRole;
      }
      
      // 两个仓库都没有找到角色信息
      LogUtil.warn('未找到角色详情，ID: $roleId');
      errorMessage.value = 'Role not found';
      return null;
      
    } catch (e) {
      LogUtil.error('获取角色详情失败: $e');
      errorMessage.value = '获取角色信息失败，请稍后再试';
      
      // 尝试从推荐仓库获取基本信息
      try {
        LogUtil.debug('尝试从推荐仓库获取基本信息，ID: $roleId');
        final basicRole = await _recommendRepository.getById(roleId);
        if (basicRole != null) {
          LogUtil.debug('成功从推荐仓库获取基本信息: ${basicRole.name}');
          currentRole.value = basicRole;
          return basicRole;
        }
      } catch (fallbackError) {
        LogUtil.error('从推荐仓库获取基本信息也失败: $fallbackError');
      }
      
      ErrorHandler.handleException(
        AppException('Failed to load role detail', originalError: e)
      );
      return null;
    } finally {
      isLoading.value = false;
    }
  }
  
  /// 收藏角色
  /// 
  /// 将指定角色添加到收藏夹
  /// [roleId] 角色ID
  /// 返回是否收藏成功
  Future<bool> favoriteRole(int roleId) async {
    try {
      isFavoriteLoading.value = true;
      
      LogUtil.debug('收藏角色, ID: $roleId');
      
      // 调用仓库层收藏角色
      final result = await _repository.favoriteRole(roleId);
      
      if (result != null && result['success'] == true) {
        LogUtil.debug('成功收藏角色');
        
        // 更新当前角色的收藏状态
        if (currentRole.value != null && currentRole.value!.id == roleId) {
          // 解析收藏时间
          DateTime? favoritedAt;
          if (result['favorited_at'] != null) {
            try {
              favoritedAt = DateTime.parse(result['favorited_at'].toString());
            } catch (e) {
              LogUtil.warn('解析收藏时间失败: ${result['favorited_at']}');
              favoritedAt = DateTime.now();
            }
          } else {
            favoritedAt = DateTime.now();
          }
          
          // 更新角色对象
          currentRole.value = currentRole.value!.copyWith(
            isFavorited: true,
            favoritedAt: favoritedAt,
          );
        }
        
        return true;
      } else {
        LogUtil.warn('收藏角色失败');
        return false;
      }
    } catch (e) {
      LogUtil.error('收藏角色异常: $e');
      errorMessage.value = '收藏角色失败，请稍后再试';
      ErrorHandler.handleException(e);
      return false;
    } finally {
      isFavoriteLoading.value = false;
    }
  }
  
  /// 取消收藏角色
  /// 
  /// 将指定角色从收藏夹中移除
  /// [roleId] 角色ID
  /// 返回是否取消成功
  Future<bool> unfavoriteRole(int roleId) async {
    try {
      isFavoriteLoading.value = true;
      
      LogUtil.debug('取消收藏角色, ID: $roleId');
      
      // 调用仓库层取消收藏角色
      final success = await _repository.unfavoriteRole(roleId);
      
      if (success) {
        LogUtil.debug('成功取消收藏角色');
        
        // 更新当前角色的收藏状态
        if (currentRole.value != null && currentRole.value!.id == roleId) {
          currentRole.value = currentRole.value!.copyWith(
            isFavorited: false,
            favoritedAt: null,
          );
        }
        
        return true;
      } else {
        LogUtil.warn('取消收藏角色失败');
        return false;
      }
    } catch (e) {
      LogUtil.error('取消收藏角色异常: $e');
      errorMessage.value = '取消收藏角色失败，请稍后再试';
      ErrorHandler.handleException(e);
      return false;
    } finally {
      isFavoriteLoading.value = false;
    }
  }
  
  /// 切换收藏状态
  /// 
  /// 如果角色已收藏则取消收藏，否则收藏角色
  /// [roleId] 角色ID
  /// 返回操作后的收藏状态(true=已收藏, false=未收藏)
  Future<bool?> toggleFavorite(int roleId) async {
    try {
      // 获取当前角色
      final role = currentRole.value;
      
      // 如果当前没有角色信息，先获取角色详情
      if (role == null || role.id != roleId) {
        final roleInfo = await getRoleDetail(roleId);
        if (roleInfo == null) {
          return null;
        }
      }
      
      // 根据当前收藏状态执行相应操作
      final isFavorited = currentRole.value?.isFavorited ?? false;
      
      if (isFavorited) {
        // 已收藏，执行取消收藏
        final success = await unfavoriteRole(roleId);
        return success ? false : null;
      } else {
        // 未收藏，执行收藏
        final success = await favoriteRole(roleId);
        return success ? true : null;
      }
    } catch (e) {
      LogUtil.error('切换收藏状态失败: $e');
      return null;
    }
  }
  
  /// 获取收藏的角色列表
  /// [page] 页码，从1开始
  /// [size] 每页大小
  /// 返回分页的收藏角色列表数据
  Future<Map<String, dynamic>> getFavoritedRoles({int page = 1, int size = 10}) async {
    try {
      LogUtil.debug('获取收藏角色列表，page: $page, size: $size');
      return await _repository.getFavoritedRoles(page: page, size: size);
    } catch (e) {
      LogUtil.error('获取收藏角色列表失败: $e');

      // 返回空数据结构
      return {
        'items': <AiRole>[],
        'total': 0,
        'page': page,
        'size': size,
        'pages': 0
      };
    }
  }
  
  /// 清除当前角色信息
  void clearCurrentRole() {
    currentRole.value = null;
  }
}
