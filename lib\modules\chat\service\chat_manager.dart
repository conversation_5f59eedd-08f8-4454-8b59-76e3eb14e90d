import 'package:get/get.dart';
import 'package:rolio/common/constants/error_codes.dart';
import 'package:rolio/common/constants/string_constants.dart';
import 'package:rolio/common/constants/ws_constants.dart';
import 'package:rolio/common/event/event_bus.dart';
import 'package:rolio/common/interfaces/session_provider.dart';
import 'package:rolio/common/services/role_provider.dart';
import 'package:rolio/common/utils/error_handler.dart';
import 'package:rolio/common/utils/logger.dart';
import 'package:rolio/common/models/ai_role.dart';
import 'package:rolio/common/models/user.dart' as app;
import 'package:rolio/manager/ws_manager.dart';
import 'package:rolio/manager/global_state.dart';
import 'package:rolio/common/services/session_binding_service.dart';
import 'package:rolio/modules/chat/service/ai_channel_manager.dart';

/// 会话管理服务
///
/// 负责会话切换、角色管理和WebSocket通道管理
class ChatManager extends GetxService {
  // 依赖服务
  final RoleProvider _roleService;
  final WsManager _wsManager = Get.find<WsManager>();
  final GlobalState _globalState = Get.find<GlobalState>();
  final EventBus _eventBus = Get.find<EventBus>();
  
  // 会话状态
  final RxInt activeConversationId = RxInt(0);
  final RxInt activeAiRoleId = RxInt(0);
  final RxBool isConnected = RxBool(false);
  
  // 角色状态
  final Rx<AiRole?> currentRole = Rx<AiRole?>(null);
  final RxBool isLoadingRoleInfo = false.obs;
  
  // 构造函数
  ChatManager({required RoleProvider roleService})
      : _roleService = roleService;
  
  // 获取AiChannelManager实例
  AiChannelManager get _channelManager {
    if (!Get.isRegistered<AiChannelManager>()) {
      Get.put(AiChannelManager());
    }
    return Get.find<AiChannelManager>();
  }
  
  @override
  void onInit() {
    super.onInit();
    
    // 监听WebSocket连接状态变化
    _wsManager.connectionStateStream.listen((state) {
      isConnected.value = state == WsConnectionState.connected;
      LogUtil.debug('WebSocket连接状态变化: $state, isConnected=${isConnected.value}');
    });
    
    // 尝试初始化全局WebSocket连接
    _initializeGlobalConnection();
  }
  
  /// 初始化全局WebSocket连接
  Future<void> _initializeGlobalConnection() async {
    try {
      // 如果已连接，不需要重新连接
      if (_wsManager.isConnected) {
        LogUtil.debug('全局WebSocket连接已存在，无需重新初始化');
        return;
      }
      
      // 获取用户ID
      var userId = _globalState.currentUser.value?.uid;
      if (userId == null || userId.isEmpty) {
        LogUtil.warn('用户ID为空，使用默认用户ID: test_user');
        userId = 'test_user';
        
        // 创建一个默认用户并设置到全局状态
        final defaultUser = app.User(
          uid: userId,
          userName: 'Test User',
          isAnonymous: true,
        );
        _globalState.setCurrentUser(defaultUser);
      }
      
      // 建立全局WebSocket连接
      final connected = await _wsManager.connectGlobal(userId: userId);
      
      if (connected) {
        LogUtil.info('全局WebSocket连接已初始化 - 用户ID: $userId');
      } else {
        LogUtil.warn('全局WebSocket连接初始化失败，将在需要时重试');
      }
    } catch (e) {
      LogUtil.error('初始化全局WebSocket连接失败: $e');
      // 不抛出异常，允许应用继续运行
    }
  }
  
  /// 预设角色信息
  /// 
  /// 在切换会话前调用，确保UI能立即显示正确的角色信息
  /// [role] 预设的角色信息
  void presetRoleInfo(AiRole role) {
    LogUtil.debug('预设角色信息: ${role.name}, 头像: ${role.avatarUrl}');
    updateCurrentRole(role);
  }
  
  /// 更新当前角色信息
  /// 
  /// 统一的角色信息更新入口，所有需要更新角色信息的地方都应该调用这个方法，确保角色信息的一致性
  /// [role] 新的角色信息
  void updateCurrentRole(AiRole? role) {
    try {
      if (role == null) {
        LogUtil.warn('尝试更新为空角色信息');
        return;
      }
      
      LogUtil.debug('更新角色信息: 角色ID=${role.id}, 角色名=${role.name}');
      
      // 更新当前角色
      currentRole.value = role;
      
      // 更新全局状态中的当前AI角色
      _globalState.setCurrentAiRole(role);
      
      // 如果角色ID与当前活跃角色ID不一致，更新活跃角色ID
      if (activeAiRoleId.value != role.id) {
        LogUtil.debug('角色ID不匹配，自动更新活跃角色ID: 旧=${activeAiRoleId.value}, 新=${role.id}');
        activeAiRoleId.value = role.id;
      }
    } catch (e) {
      LogUtil.error('更新角色信息失败: $e');
      // 角色信息更新失败是内部错误，不直接向用户显示
    }
  }
  
  /// 切换会话
  /// 
  /// [conversationId] 会话ID
  /// [aiRoleId] AI角色ID
  Future<void> switchConversation(int conversationId, int aiRoleId) async {
    try {
      // 记录切换的会话信息
      LogUtil.info('切换会话: 从会话ID=${activeConversationId.value}(角色ID=${activeAiRoleId.value})切换到会话ID=$conversationId(角色ID=$aiRoleId)');
      
      // 验证会话ID - 允许conversationId为0，表示新会话
      if (conversationId < 0) {
        throw ErrorHandler.createAppException('invalid conversation id', ErrorCodes.DATA_NOT_FOUND);
      }
      
      // 验证角色ID
      if (aiRoleId <= 0) {
        throw ErrorHandler.createAppException('invalid role id', ErrorCodes.DATA_NOT_FOUND);
      }
      
      // 如果conversationId为0，表示这是新会话或没有历史对话的角色
      if (conversationId == 0) {
        LogUtil.info('切换到新会话或无历史对话的角色，角色ID=$aiRoleId');
      }
      
      // 尝试确保全局WebSocket连接已建立，但不阻止角色切换
      bool wsConnected = false;
      if (!_wsManager.isConnected) {
        // 获取用户ID
        var userId = _globalState.currentUser.value?.uid;
        if (userId == null || userId.isEmpty) {
          LogUtil.warn('用户ID为空，使用默认用户ID: test_user');
          userId = 'test_user';
          
          // 创建一个默认用户并设置到全局状态
          final defaultUser = app.User(
            uid: userId,
            userName: 'Test User',
            isAnonymous: true,
          );
          _globalState.setCurrentUser(defaultUser);
        }
        
        try {
          // 尝试建立全局连接，但不要阻止角色切换
          wsConnected = await _wsManager.connectGlobal(userId: userId);
          if (!wsConnected) {
            // 只记录警告，不抛出异常
            LogUtil.warn('无法建立WebSocket连接，但仍将继续切换角色');
          } else {
            LogUtil.debug('已建立全局WebSocket连接');
          }
        } catch (e) {
          // 只记录错误，不抛出异常
          LogUtil.error('WebSocket连接失败: $e');
          LogUtil.warn('WebSocket连接失败，但仍将继续切换角色');
          wsConnected = false;
        }
      } else {
        wsConnected = true;
      }
      
      // 清理当前会话的订阅，防止积累
      final currentAiRoleId = activeAiRoleId.value;
      if (currentAiRoleId > 0 && currentAiRoleId != aiRoleId) {
        // 通知AiChannelManager清理旧角色的过多订阅
        if (Get.isRegistered<AiChannelManager>()) {
          final channelManager = Get.find<AiChannelManager>();
          LogUtil.debug('切换会话前清理旧角色订阅: roleId=$currentAiRoleId');
          channelManager.unsubscribeFromRole(currentAiRoleId);
        }
      }
      
      // 保存当前角色信息，避免被重置
      final currentRoleInfo = currentRole.value;
      
      // 设置新的会话ID和角色ID
      setActiveSession(conversationId, aiRoleId);
      LogUtil.debug('已设置新的会话ID=$conversationId和角色ID=$aiRoleId');
      
      // 只有在WebSocket连接成功时才进行通道切换操作
      if (wsConnected) {
        // 使用WsManager切换通道 - 用于发送消息
        _wsManager.switchChannel(conversationId);
        LogUtil.debug('已切换WebSocket通道到会话ID=$conversationId');
      } else {
        LogUtil.debug('WebSocket未连接，跳过通道切换');
      }
      
      // 如果有预设的角色信息，恢复它
      if (currentRoleInfo != null && currentRoleInfo.id == aiRoleId) {
        updateCurrentRole(currentRoleInfo);
        LogUtil.debug('恢复预设的角色信息: ${currentRoleInfo.name}');
      }
      
      // 如果没有预设的角色信息，才加载角色信息
      if (currentRoleInfo == null || currentRoleInfo.id != aiRoleId) {
        await loadRoleInfo(aiRoleId);
      }
      
      // 验证会话ID和角色ID是否正确设置
      LogUtil.debug('切换会话完成，当前会话ID=${activeConversationId.value}，角色ID=${activeAiRoleId.value}');
    } catch (e) {
      LogUtil.error('切换会话失败: $e');
      rethrow;
    }
  }
  
  /// 设置活跃会话
  /// 
  /// [conversationId] 会话ID
  /// [aiRoleId] AI角色ID
  void setActiveSession(int conversationId, int aiRoleId) {
    activeConversationId.value = conversationId;
    
    // 只有当角色ID发生变化时才发送事件
    if (activeAiRoleId.value != aiRoleId) {
      activeAiRoleId.value = aiRoleId;
      
      // 发送角色变更事件
      _eventBus.fire(AppEvent.activeRoleChanged, {
        'roleId': aiRoleId,
        'previousRoleId': activeAiRoleId.value
      });
      
      LogUtil.debug('已发送角色变更事件: roleId=$aiRoleId');
    }
  }
  
  /// 处理WebSocket返回的会话数据
  /// 
  /// 当用户首次向没有conversation_id的角色发送消息时，
  /// WebSocket会返回新创建的conversation_id
  /// 
  /// [data] WebSocket返回的数据
  void handleWebSocketSessionInfo(Map<String, dynamic> data) {
    try {
      // 提取role_id - 优先处理
      int? roleId;
      if (data.containsKey('role_id')) {
        roleId = int.tryParse(data['role_id'].toString());
      }
      
      // 提取conversation_id
      int? conversationId;
      if (data.containsKey('conversation_id')) {
        conversationId = int.tryParse(data['conversation_id'].toString());
      }
      
      // 日志记录接收到的信息
      LogUtil.info('收到WebSocket返回的会话信息: conversationId=$conversationId, roleId=$roleId');
      
      // 确保角色ID有效 - 如果没有有效的role_id，不处理
      if (roleId == null || roleId <= 0) {
        LogUtil.warn('收到的WebSocket会话信息中没有有效的角色ID，使用当前角色ID');
        roleId = activeAiRoleId.value;
        
        // 如果当前角色ID也无效，则退出
        if (roleId <= 0) {
          LogUtil.error('当前角色ID无效，无法处理WebSocket会话信息');
          return;
        }
      }
      
      // 使用有效的会话ID，如果没有则保持当前会话ID
      int useConversationId;
      if (conversationId != null && conversationId > 0) {
        // 如果收到了有效的conversation_id，优先使用它
        useConversationId = conversationId;
        LogUtil.info('使用服务器返回的会话ID: $useConversationId');
      } else {
        // 如果没有收到有效的conversation_id，使用当前活跃的会话ID
        useConversationId = activeConversationId.value;
        LogUtil.info('未收到有效的会话ID，使用当前会话ID: $useConversationId');
      }
      
      // 检查收到的消息是否属于当前活跃角色
      bool isActiveRole = (roleId == activeAiRoleId.value);
      
      // 记录会话ID变更
      if (isActiveRole && activeConversationId.value != useConversationId) {
        LogUtil.info('会话ID变更: 从${activeConversationId.value}变更为$useConversationId');
      }
      
      // 只有当收到的消息属于当前活跃角色时，才切换会话
      if (isActiveRole) {
        // 更新当前会话ID和角色ID
        setActiveSession(useConversationId, roleId);
        
        // 如果有有效的会话ID，切换到对应通道
        if (useConversationId > 0) {
          _wsManager.switchChannel(useConversationId);
          LogUtil.debug('已切换到会话通道: $useConversationId');
        } else {
          // 如果没有有效的会话ID，切换到全局通道
          _wsManager.switchToGlobalChannel();
          LogUtil.debug('无有效会话ID，已切换到全局通道');
        }
      } else {
        LogUtil.debug('收到非当前活跃角色的消息: received role_id=$roleId, current role_id=${activeAiRoleId.value}');
      }
      
      // 无论是否是当前活跃角色，都更新角色会话绑定缓存
      // 使用SessionBindingService进行角色会话绑定
      if (Get.isRegistered<SessionBindingService>()) {
        final bindingService = Get.find<SessionBindingService>();
        // 只有当会话ID有效时才进行绑定
        if (useConversationId > 0) {
          bindingService.bindRoleToConversation(roleId, useConversationId);
          LogUtil.debug('通过SessionBindingService绑定角色会话: roleId=$roleId, conversationId=$useConversationId');
        }
      } else {
        // 如果SessionBindingService未注册，使用旧方法
        _updateRoleConversationCache(roleId, useConversationId);
        LogUtil.debug('SessionBindingService未注册，使用旧方法更新角色缓存');
      }
      
      // 通知角色列表更新
      _notifyRoleListUpdate(roleId, useConversationId);
      
      LogUtil.debug('已完成会话信息处理: conversationId=$useConversationId, roleId=$roleId');
    } catch (e) {
      LogUtil.error('处理WebSocket会话信息失败: $e');
    }
  }
  
  /// 更新角色会话缓存
  void _updateRoleConversationCache(int roleId, int conversationId) {
    try {
      final roleService = _roleService;
      roleService.updateRoleConversationId(roleId, conversationId);
      LogUtil.debug('已更新角色缓存: roleId=$roleId, conversationId=$conversationId');
    } catch (e) {
      LogUtil.error('更新角色缓存失败: $e');
    }
  }
  
  /// 通知角色列表更新
  void _notifyRoleListUpdate(int roleId, int conversationId) {
    try {
      // 使用全局状态通知角色更新
      _globalState.notifyRoleConversationUpdate(roleId, conversationId);
      LogUtil.debug('已通知角色列表更新');
    } catch (e) {
      LogUtil.error('通知角色列表更新失败: $e');
    }
  }
  
  /// 加载角色信息
  /// 
  /// 根据角色ID获取角色详细信息
  /// [aiRoleId] 角色ID
  /// 返回角色信息，如果获取失败则返回null
  Future<AiRole?> loadRoleInfo(int aiRoleId) async {
    try {
      LogUtil.debug('加载角色信息: aiRoleId=$aiRoleId');
      
      // 设置加载状态
      isLoadingRoleInfo.value = true;
      
      // 检查当前角色信息是否已存在
      if (currentRole.value != null && currentRole.value!.id == aiRoleId) {
        LogUtil.debug('当前角色信息已存在，无需重新加载');
        return currentRole.value;
      }
      
      // 使用RoleService获取角色信息
      String? avatarUrl;
      String? coverUrl;
      String? roleName;
      
      try {
        avatarUrl = await _roleService.getAvatarUrlById(aiRoleId);
        coverUrl = await _roleService.getCoverUrlById(aiRoleId);
        roleName = await _roleService.getRoleNameById(aiRoleId);
      } catch (e) {
        LogUtil.error('通过RoleService获取角色信息失败: $e，使用默认值');
        // 使用默认值
        avatarUrl = StringsConsts.recommendDefaultAvatarUrl;
        coverUrl = StringsConsts.recommendDefaultCoverUrl;
        roleName = 'AI助手';
      }
      
      // 如果获取到了必要信息，创建一个AiRole对象
      if (roleName != null && roleName.isNotEmpty) {
        final role = AiRole(
          id: aiRoleId,
          name: roleName,
          avatarUrl: avatarUrl ?? StringsConsts.recommendDefaultAvatarUrl,
          coverUrl: coverUrl ?? StringsConsts.recommendDefaultCoverUrl,
          description: '',  // 可以通过RoleService添加获取描述的方法
          tags: [],  // 可以通过RoleService添加获取标签的方法
          position: 0,
        );
        
        // 更新当前角色信息
        updateCurrentRole(role);
        
        LogUtil.debug('角色信息加载成功: ${role.name}, 头像: ${role.avatarUrl}');
        return role;
      }
      
      LogUtil.warn('未能获取完整的角色信息');
      return null;
    } catch (e) {
      LogUtil.error('加载角色信息失败: $e');
      
      // 使用ErrorHandler处理错误
      ErrorHandler.handleException(
        AppException('the role info load failed, please try again later', code: ErrorCodes.ROLE_INFO_LOAD_FAILED, originalError: e),
        showSnackbar: true,
      );
      return null;
    } finally {
      // 无论成功与否，都重置加载状态
      isLoadingRoleInfo.value = false;    }
  }
  
  /// 切换到下一个角色
  /// 
  /// 从RoleService获取下一个角色，并切换会话
  Future<AiRole?> switchToNextRole() async {
    try {
      final currentRoleId = activeAiRoleId.value;
      if (currentRoleId <= 0) return null;
      
      // 设置加载状态
      isLoadingRoleInfo.value = true;
      
      // 添加日志，帮助诊断问题
      LogUtil.info('向左滑动，切换到下一个角色');
      
      // 尝试获取下一个角色
      AiRole? nextRole;
      try {
        // 先尝试从RoleService获取下一个角色
        nextRole = await _roleService.getNextRole(currentRoleId, true);
        
        // 如果获取失败，尝试使用默认角色
        if (nextRole == null) {
          LogUtil.info('RoleService未返回下一个角色，使用备用角色');
          nextRole = await _getDefaultNextRole(currentRoleId);
        }
      } catch (e) {
        LogUtil.error('获取下一个角色失败: $e，使用备用角色');
        // 如果RoleService未初始化或出错，使用备用方法
        nextRole = await _getDefaultNextRole(currentRoleId);
      }
      
      if (nextRole != null) {
        LogUtil.info('切换到下一个角色: ${nextRole.name}, ID: ${nextRole.id}');
        
        // 从多个来源检查角色是否已有会话ID
        final conversationId = await _checkRoleConversationId(nextRole);
        
        if (conversationId != null) {
          // 使用现有会话ID
          LogUtil.info('使用现有会话ID: $conversationId');
          
          // 切换到现有会话
          setActiveSession(conversationId, nextRole.id);
          _wsManager.switchChannel(conversationId);
          
          // 更新当前角色信息，确保conversationId正确
          final updatedRole = nextRole.copyWith(conversationId: conversationId);
          updateCurrentRole(updatedRole);
          
          return updatedRole;
        } else {
          LogUtil.info('角色没有会话ID，将显示greeting');
          
          // 直接更新当前角色信息，不创建新会话
          setActiveSession(0, nextRole.id);  // 使用0表示没有活跃会话
          updateCurrentRole(nextRole);
          
          // 确保WsManager切换到全局通道
          _wsManager.switchToGlobalChannel();
          LogUtil.debug('已切换到全局WebSocket通道，等待新会话创建');
          
          return nextRole;
        }
      } else {
        LogUtil.warn('没有下一个角色可供切换');
        return null;
      }
    } catch (e) {
      LogUtil.error('切换到下一个角色失败: $e');
      
      // 使用ErrorHandler处理错误
      ErrorHandler.handleException(
        AppException('the role switch failed, please try again later', code: ErrorCodes.ROLE_SWITCH_FAILED, originalError: e),
        showSnackbar: true,
      );
      return null;
    } finally {
      // 无论成功与否，都重置加载状态
      isLoadingRoleInfo.value = false;
    }
  }
  
  /// 切换到上一个角色
  /// 
  /// 从RoleService获取上一个角色，并切换会话
  Future<AiRole?> switchToPreviousRole() async {
    try {
      final currentRoleId = activeAiRoleId.value;
      if (currentRoleId <= 0) return null;
      
      // 设置加载状态
      isLoadingRoleInfo.value = true;
      
      // 添加日志，帮助诊断问题
      LogUtil.info('向右滑动，切换到上一个角色');
      
      // 尝试获取上一个角色
      AiRole? previousRole;
      try {
        // 先尝试从RoleService获取上一个角色
        previousRole = await _roleService.getPreviousRole(currentRoleId, true);
        
        // 如果获取失败，尝试使用默认角色
        if (previousRole == null) {
          LogUtil.info('RoleService未返回上一个角色，使用备用角色');
          previousRole = await _getDefaultPreviousRole(currentRoleId);
        }
      } catch (e) {
        LogUtil.error('获取上一个角色失败: $e，使用备用角色');
        // 如果RoleService未初始化或出错，使用备用方法
        previousRole = await _getDefaultPreviousRole(currentRoleId);
      }
      
      if (previousRole != null) {
        LogUtil.info('切换到上一个角色: ${previousRole.name}, ID: ${previousRole.id}');
        
        // 从多个来源检查角色是否已有会话ID
        final conversationId = await _checkRoleConversationId(previousRole);
        
        if (conversationId != null) {
          // 使用现有会话ID
          LogUtil.info('使用现有会话ID: $conversationId');
          
          // 切换到现有会话
          setActiveSession(conversationId, previousRole.id);
          _wsManager.switchChannel(conversationId);
          
          // 更新当前角色信息，确保conversationId正确
          final updatedRole = previousRole.copyWith(conversationId: conversationId);
          updateCurrentRole(updatedRole);
          
          return updatedRole;
        } else {
          LogUtil.info('角色没有会话ID，将显示greeting');
          
          // 直接更新当前角色信息，不创建新会话
          setActiveSession(0, previousRole.id);  // 使用0表示没有活跃会话
          updateCurrentRole(previousRole);
          
          // 确保WsManager切换到全局通道
          _wsManager.switchToGlobalChannel();
          LogUtil.debug('已切换到全局WebSocket通道，等待新会话创建');
          
          return previousRole;
        }
      } else {
        LogUtil.warn('没有上一个角色可供切换');
        return null;
      }
    } catch (e) {
      LogUtil.error('切换到上一个角色失败: $e');
      
      // 使用ErrorHandler处理错误
      ErrorHandler.handleException(
        AppException('the role switch failed, please try again later', code: ErrorCodes.ROLE_SWITCH_FAILED, originalError: e),
        showSnackbar: true,
      );
      return null;
    } finally {
      // 无论成功与否，都重置加载状态
      isLoadingRoleInfo.value = false;
    }
  }
  
  // 获取默认的下一个角色（备用方法）
  Future<AiRole?> _getDefaultNextRole(int currentRoleId) async {
    return null;
  }
  
  // 获取默认的上一个角色（备用方法）
  Future<AiRole?> _getDefaultPreviousRole(int currentRoleId) async {
    return null;
  }

  /// 同步会话状态
  Future<void> syncConversationState() async {
    try {
      if (_wsManager.isConnected) {
        final conversationId = activeConversationId.value;
        final roleId = activeAiRoleId.value;
        
        // 只有在有效会话时进行同步
        if (conversationId > 0 && roleId > 0) {
          // 发送状态同步请求
          _wsManager.sendToChannel(
            event: WsEvent.sync_state,
            data: {
              'role_id': roleId,
              'sync_type': 'conversation_state',
              'conversationid': conversationId
            },
          );
          
          LogUtil.debug('已发送会话状态同步请求');
        }
      } else {
        LogUtil.warn('WebSocket未连接，无法同步状态');
      }
    } catch (e) {
      LogUtil.error('同步会话状态失败: $e');
    }
  }

  /// 检查角色是否有会话ID
  /// 
  /// 从多个来源检查角色是否有关联的会话ID
  /// [role] 角色对象
  /// 返回会话ID，如果没有则返回null
  Future<int?> _checkRoleConversationId(AiRole role) async {
    // 首先检查角色对象本身
    if (role.conversationId != null && role.conversationId! > 0) {
      LogUtil.debug('从角色对象中获取会话ID: ${role.conversationId}');
      
      // 检查会话是否存在
      final sessionExists = await _checkSessionExists(role.conversationId!);
      if (!sessionExists) {
        LogUtil.warn('角色关联的会话ID: ${role.conversationId} 不存在，清除绑定关系');
        // 清除绑定关系
        if (Get.isRegistered<SessionBindingService>()) {
          final bindingService = Get.find<SessionBindingService>();
          bindingService.clearBinding(role.id);
          
          // 更新角色对象的conversationId
          try {
            final updatedRole = role.copyWith(conversationId: null);
            await _roleService.updateRoleConversationId(role.id, 0);
            LogUtil.debug('已更新角色会话ID为null: roleId=${role.id}');
          } catch (e) {
            LogUtil.error('更新角色会话ID失败: $e');
          }
        }
        return null;
      }
      
      return role.conversationId;
    }
    
    // 检查SessionBindingService
    if (Get.isRegistered<SessionBindingService>()) {
      final bindingService = Get.find<SessionBindingService>();
      final conversationId = bindingService.getConversationIdForRole(role.id);
      if (conversationId != null && conversationId > 0) {
        LogUtil.debug('从SessionBindingService获取会话ID: $conversationId');
        
        // 检查会话是否存在
        final sessionExists = await _checkSessionExists(conversationId);
        if (!sessionExists) {
          LogUtil.warn('SessionBindingService中的会话ID: $conversationId 不存在，清除绑定关系');
          // 清除绑定关系
          bindingService.clearBinding(role.id);
          return null;
        }
        
        return conversationId;
      }
    }
    
    // 尝试从RoleService获取最新角色信息
    try {
      final updatedRole = await _roleService.getRoleById(role.id);
      if (updatedRole != null && updatedRole.conversationId != null && updatedRole.conversationId! > 0) {
        LogUtil.debug('从RoleService获取会话ID: ${updatedRole.conversationId}');
        
        // 检查会话是否存在
        final sessionExists = await _checkSessionExists(updatedRole.conversationId!);
        if (!sessionExists) {
          LogUtil.warn('RoleService中的会话ID: ${updatedRole.conversationId} 不存在，清除绑定关系');
          // 清除绑定关系
          if (Get.isRegistered<SessionBindingService>()) {
            final bindingService = Get.find<SessionBindingService>();
            bindingService.clearBinding(role.id);
            
            // 更新角色对象的conversationId
            try {
              await _roleService.updateRoleConversationId(role.id, 0);
              LogUtil.debug('已更新角色会话ID为null: roleId=${role.id}');
            } catch (e) {
              LogUtil.error('更新角色会话ID失败: $e');
            }
          }
          return null;
        }
        
        return updatedRole.conversationId;
      }
    } catch (e) {
      LogUtil.error('从RoleService获取最新角色信息失败: $e');
    }
    
    // 没有找到会话ID
    LogUtil.debug('未找到角色关联的会话ID: roleId=${role.id}');
    return null;
  }
  
  /// 检查会话是否存在
  /// 
  /// 优化：不再通过getSessionById检查会话是否存在，而是假设会话存在
  /// 如果会话不存在，后续加载历史消息时会处理这种情况
  Future<bool> _checkSessionExists(int conversationId) async {
    // 只要会话ID有效，就认为会话存在
    // 后续加载历史消息时会验证会话是否真的存在
    if (conversationId > 0) {
      LogUtil.debug('会话ID有效，假设会话存在: $conversationId');
      return true;
    }
    return false;
  }
}