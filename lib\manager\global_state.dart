import 'package:get/get.dart';
import 'package:rolio/common/models/user.dart' as app;
import 'package:rolio/common/models/ai_role.dart';
import 'package:rolio/common/utils/http_manager_util.dart';
import 'package:rolio/common/utils/logger.dart';
import 'package:rolio/common/cache/secure_storage.dart'; // 导入SecureStorage
import 'dart:math'; // 导入min函数
import 'package:rolio/common/event/event_bus.dart'; // 导入EventBus

/// 全局状态管理
///
/// 负责管理应用的全局状态，如当前用户、认证状态等
class GlobalState extends GetxController {
  /// 构造函数
  GlobalState() {
    _setupTokenExpirationListener();
  }

  /// 设置token过期监听器
  void _setupTokenExpirationListener() {
    EventBus().on('token_expired').listen((data) {
      LogUtil.info('GlobalState收到token过期事件，执行清理操作');
      // 清理所有token，该方法会同步清理SecureStorage和HttpManager中的token
      clearAccessToken().catchError((error) {
        LogUtil.error('处理token过期事件时清除token失败: $error');
      });
      
      // 可选：根据需要触发其他处理逻辑，如提示用户重新登录
      // 此处只负责清理token，不处理具体的重登录逻辑，避免耦合
      
      // 发送通知，让相关组件知道token已过期
      EventBus().fire('token_cleaned', {
        'timestamp': DateTime.now().millisecondsSinceEpoch,
        'reason': 'token_expired_event_handled'
      });
    });
  }

  /// 当前用户
  final Rx<app.User?> currentUser = Rx<app.User?>(null);

  /// 访问令牌
  final RxString accessToken = RxString('');

  /// 初始化状态
  final Rx<InitState> initState = Rx<InitState>(InitState.initializing);
  
  /// 认证流程状态
  final RxBool isAuthProcessing = RxBool(false);

  /// 最近删除的会话ID
  final RxString lastDeletedconversationid = RxString('');

  /// 当前选中的AI角色
  final Rx<AiRole?> currentAiRole = Rx<AiRole?>(null);

  /// AI是否正在生成回复
  final RxBool isAiGenerating = RxBool(false);

  /// 用户是否已登录
  bool get isLoggedIn =>
      currentUser.value != null && accessToken.value.isNotEmpty;

  /// 设置当前用户
  void setCurrentUser(app.User user) {
    currentUser.value = user;
  }

  /// 设置访问令牌（异步版本，统一入口）
  Future<void> setAccessToken(String token) async {
    if (accessToken.value == token) {
      // 如果token未变化，不重复设置
      return;
    }

    // 记录token长度，便于调试
    final tokenLength = token.length;
    LogUtil.debug('GlobalState设置token，长度: $tokenLength');

    // 1. 设置token到全局状态
    accessToken.value = token;

    // 2. 异步更新HttpManager中的token
    await HttpManager.setTokenAsync(token);
    
    // 3. 保存到安全存储
    try {
      if (Get.isRegistered<SecureStorage>()) {
        final secureStorage = Get.find<SecureStorage>();
        await secureStorage.saveToken(token);
        LogUtil.debug('已同步Token到安全存储');
      }
    } catch (e) {
      LogUtil.error('同步Token到安全存储失败: $e');
    }
  }
  


  /// 清除访问令牌（异步版本，统一入口）
  Future<void> clearAccessToken() async {
    final hadToken = accessToken.value.isNotEmpty;
    if (hadToken) {
      LogUtil.debug('清除访问令牌: ${accessToken.value.substring(0, min(10, accessToken.value.length))}...');
    }

    // 1. 清除全局状态中的token
    accessToken.value = '';

    // 2. 异步清除HttpManager中的token
    await HttpManager.clearTokenAsync();
    LogUtil.debug('已同步清除HttpManager中的token');
    
    // 3. 清除SecureStorage中的token
    try {
      if (Get.isRegistered<SecureStorage>()) {
        final secureStorage = Get.find<SecureStorage>();
        await secureStorage.deleteToken();
        LogUtil.debug('已同步清除SecureStorage中的token');
      }
    } catch (e) {
      LogUtil.error('清除SecureStorage中的Token失败: $e');
    }
  }
  


  /// 清除当前用户（异步版本，推荐使用）
  Future<void> clearCurrentUser() async {
    LogUtil.debug('清除当前用户信息和访问令牌');

    // 清除用户信息
    currentUser.value = null;

    // 清除令牌 - 使用统一的清除方法
    await clearAccessToken();

    // 清除当前AI角色
    clearCurrentAiRole();
  }
  
  

  /// 设置初始化状态
  void setInitState(InitState state) {
    initState.value = state;
  }

  /// 通知会话被删除
  void notifySessionDeleted(String conversationid) {
    lastDeletedconversationid.value = conversationid;
    // 触发事件，可以被其他控制器监听
    update(['session_deleted']);
  }

  /// 设置当前选中的AI角色
  void setCurrentAiRole(AiRole role) {
    currentAiRole.value = role;
    update(['current_ai_role']);
  }

  /// 清除当前选中的AI角色
  void clearCurrentAiRole() {
    currentAiRole.value = null;
  }

  /// 设置AI生成状态
  void setAiGeneratingState(bool isGenerating) {
    isAiGenerating.value = isGenerating;
    update(['ai_generating_state']);
  }

  /// 通知角色会话ID更新
  void notifyRoleConversationUpdate(int roleId, int conversationId) {
    // 触发事件，可以被其他控制器监听
    update(['role_conversation_updated', 'role_$roleId']);
  }
}

/// 应用初始化状态
enum InitState {
  /// 正在初始化
  initializing,

  /// 初始化完成
  completed,

  /// 初始化出错
  error,
}
