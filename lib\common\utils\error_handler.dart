import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:rolio/common/utils/logger.dart';
import 'package:rolio/common/utils/http_manager_util.dart';
import 'package:rolio/common/constants/error_codes.dart';
import 'package:rolio/common/constants/message_constants.dart';
import 'package:rolio/widgets/network_error_view.dart';
import 'package:rolio/common/utils/toast_util.dart';

/// 应用异常类
class AppException implements Exception {
  final String message;
  final String? code;
  final dynamic originalError;

  AppException(this.message, {String? code, this.originalError})
      : this.code = code ?? ErrorCodes.GENERAL_ERROR;

  @override
  String toString() => 'AppException: $code - $message';
}

/// 网络异常类
class NetworkException extends AppException {
  NetworkException(String message, {String? code, dynamic originalError})
      : super(message, code: code ?? 'NETWORK_ERROR', originalError: originalError);
}

/// 服务器异常类
class ServerException extends AppException {
  ServerException(String message, {String? code, dynamic originalError})
      : super(message, code: code ?? 'SERVER_ERROR', originalError: originalError);
}

/// 认证异常类
class AuthException extends AppException {
  AuthException(String message, {String? code, dynamic originalError})
      : super(message, code: code ?? 'AUTH_ERROR', originalError: originalError);
}

/// 数据异常类
class DataException extends AppException {
  DataException(String message, {String? code, dynamic originalError})
      : super(message, code: code ?? 'DATA_ERROR', originalError: originalError);
}

/// WebSocket异常类
class WebSocketException extends AppException {
  WebSocketException(String message, {String? code, dynamic originalError})
      : super(message, code: code ?? ErrorCodes.WS_CONNECTION_FAILED, originalError: originalError);
}

/// 错误处理工具类
/// 
/// 提供统一的错误处理方法，包括日志记录和用户界面反馈
/// 注意：推荐使用handleException方法作为统一的错误处理入口
/// handleError方法将被重定向到handleException以保持处理一致性
class   ErrorHandler {
  /// 错误代码到用户消息的映射
  static const Map<String, String> _errorMessages = {
    // 通用错误
    ErrorCodes.GENERAL_ERROR: MessageConstants.unknownError,
    ErrorCodes.NETWORK_ERROR: MessageConstants.networkError,
    ErrorCodes.SERVER_ERROR: MessageConstants.requestFailed,
    ErrorCodes.AUTH_ERROR: MessageConstants.errorMessage,
    ErrorCodes.BUSINESS_ERROR: MessageConstants.operationFailed,
    ErrorCodes.DATA_NOT_FOUND: MessageConstants.loadFailed,
    ErrorCodes.DATA_PARSE_ERROR: MessageConstants.errorMessage,
    ErrorCodes.DATA_INCONSISTENCY: MessageConstants.errorMessage,
    ErrorCodes.DUPLICATE_REQUEST: MessageConstants.errorMessage,
    ErrorCodes.RATE_LIMIT_ERROR: '请求次数过多，请稍后再试',
    
    // WebSocket相关错误
    ErrorCodes.WS_CONNECTION_FAILED: MessageConstants.connectionFailedMessage,
    ErrorCodes.WS_CONNECTION_LOST: MessageConstants.connectionLostMessage,
    ErrorCodes.WS_MESSAGE_SEND_FAILED: MessageConstants.requestFailed,
    
    // 聊天模块错误
    ErrorCodes.CHAT_SEND_FAILED: MessageConstants.operationFailed,
    ErrorCodes.CHAT_HISTORY_LOAD_FAILED: MessageConstants.loadHistoryFailedMessage,
    ErrorCodes.CHAT_START_FAILED: MessageConstants.enterChatFailedMessage,
    ErrorCodes.SESSION_SWITCH_FAILED: MessageConstants.errorMessage,
    
    // 角色模块错误
    ErrorCodes.ROLE_SWITCH_FAILED: MessageConstants.switchRoleFailedMessage,
    ErrorCodes.ROLE_INFO_LOAD_FAILED: MessageConstants.loadFailed,
    ErrorCodes.ROLE_LIST_LOAD_FAILED: MessageConstants.loadRecommendedRolesFailedMessage,
  };
  
  /// 根据错误代码获取用户友好的错误消息
  static String getMessageForErrorCode(String errorCode) {
    return _errorMessages[errorCode] ?? MessageConstants.unknownError;
  }

  /// 处理错误
  /// 
  /// [error] 错误对象
  /// [message] 显示给用户的错误消息
  /// [logMessage] 日志消息
  /// [showSnackbar] 是否显示Snackbar
  /// [errorCode] 错误代码
  /// [onRetry] 重试回调
  /// 
  /// 注意：推荐使用handleException作为统一错误处理入口
  static void handleError(
    dynamic error, {
    String? message,
    String? logMessage,
    bool showSnackbar = true,
    VoidCallback? onRetry,
    String? errorCode,
  }) {
    // 记录错误日志
    final errorCodeStr = errorCode != null ? "[$errorCode] " : "";
    LogUtil.error('${errorCodeStr}${logMessage ?? 'error'}: $error');
    
    // 确定显示消息
    final displayMessage = message ?? 
        (errorCode != null ? getMessageForErrorCode(errorCode) : MessageConstants.unknownError);
    
    // 显示错误消息 - 直接实现，不调用handleException避免循环调用
    if (showSnackbar) {
      ToastUtil.error(displayMessage);
      
      // 如果有重试按钮，我们需要额外处理，因为ToastUtil不直接支持按钮
      if (onRetry != null) {
        // 使用延迟执行，避免两个提示框同时显示
        Future.delayed(const Duration(milliseconds: 3000), () {
          ToastUtil.showToast(
            MessageConstants.retryLoadingMessage,
            type: ToastType.warning,
            duration: const Duration(seconds: 2),
          );
        });
      }
    }
  }
  
  /// 创建应用异常
  /// 
  /// [error] 原始错误对象
  /// [message] 错误消息
  static AppException createAppException(dynamic error, [String? message]) {
    if (error is AppException) {
      return error;
    }
    
    // 根据错误类型创建不同的异常
    if (error.toString().toLowerCase().contains('timeout') ||
        error.toString().toLowerCase().contains('connection') ||
        error.toString().toLowerCase().contains('network')) {
      return NetworkException(message ?? MessageConstants.networkError, code: ErrorCodes.NETWORK_ERROR);
    } else if (error.toString().toLowerCase().contains('unauthorized') ||
               error.toString().toLowerCase().contains('forbidden') ||
               error.toString().toLowerCase().contains('permission')) {
      return AuthException(message ?? MessageConstants.errorMessage, code: ErrorCodes.AUTH_ERROR);
    } else if (error.toString().toLowerCase().contains('not found') ||
               error.toString().toLowerCase().contains('404')) {
      return ServerException(message ?? MessageConstants.loadFailed, code: ErrorCodes.DATA_NOT_FOUND);
    } else if (error.toString().toLowerCase().contains('parse') ||
               error.toString().toLowerCase().contains('format') ||
               error.toString().toLowerCase().contains('type')) {
      return DataException(message ?? MessageConstants.errorMessage, code: ErrorCodes.DATA_PARSE_ERROR);
    } else {
      return AppException(message ?? MessageConstants.unknownError, code: ErrorCodes.GENERAL_ERROR, originalError: error);
    }
  }
  
  /// 处理网络错误
  static void handleNetworkError(
    dynamic error, {
    String message = 'Network connection error, please check your network and try again',
    bool showSnackbar = true,
    bool showFullScreen = false,
    VoidCallback? onRetry,
    BuildContext? context,
  }) {
    // 创建NetworkException对象
    final networkException = NetworkException(
      message,
      code: ErrorCodes.NETWORK_ERROR,
      originalError: error
    );
    
    // 使用统一的handleException处理错误
    handleException(
      networkException,
      message: message,
      showSnackbar: showSnackbar && !showFullScreen,
      onRetry: onRetry,
    );
    
    // 保留特殊处理：显示全屏网络错误页面
    if (showFullScreen && context != null && onRetry != null) {
      showNetworkErrorPage(context, onRetry: onRetry, errorMessage: message);
    }
  }
  
  /// 处理API错误
  static void handleApiError(
    dynamic error, {
    String? message,
    int? errorCode, // 保持向后兼容，但优先使用异常对象中的信息
    bool showSnackbar = true,
  }) {
    String displayMessage;
    String logMessage;
    String errorCodeStr = (errorCode != null) ? errorCode.toString() : ErrorCodes.SERVER_ERROR;
    
    // 根据错误类型创建适当的异常对象
    AppException appException;
    if (error is ApiException) {
      displayMessage = message ?? error.message;
      logMessage = 'API error(${error.code}): ${error.message}';
      appException = ServerException(
        displayMessage,
        code: error.errorCode,
        originalError: error
      );
    } else if (error is AppException) {
      displayMessage = message ?? error.message;
      logMessage = 'API error(${error.code ?? 'UNKNOWN'}): ${error.message}';
      appException = error;
    } else {
      displayMessage = message ?? 'Request failed, please try again later';
      logMessage = errorCode != null ? 'API error($errorCode)' : 'API error: $error';
      appException = ServerException(
        displayMessage, 
        code: errorCodeStr,
        originalError: error
      );
    }
    
    // 使用统一的handleException处理错误
    handleException(
      appException,
      message: displayMessage,
      showSnackbar: showSnackbar,
    );
  }
  
  /// 处理WebSocket错误
  static void handleWebSocketError(
    dynamic error, {
    String? message,
    bool showSnackbar = true,
    VoidCallback? onRetry,
    String? errorCode,
    bool showFullScreen = false,
    BuildContext? context,
  }) {
    String displayMessage = message ?? 'There is an error';
    String actualErrorCode = errorCode ?? ErrorCodes.WS_CONNECTION_FAILED;
    
    // 创建WebSocketException对象
    WebSocketException wsException;
    if (error is WebSocketException) {
      wsException = error;
    } else if (error is AppException) {
      wsException = WebSocketException(
        message ?? error.message,
        code: error.code ?? actualErrorCode,
        originalError: error.originalError
      );
    } else {
      wsException = WebSocketException(
        displayMessage,
        code: actualErrorCode,
        originalError: error
      );
    }
    
    // 使用统一的handleException处理错误
    handleException(
      wsException,
      message: displayMessage,
      showSnackbar: showSnackbar && !showFullScreen,
      onRetry: onRetry,
    );
    
    // 保留特殊处理：显示全屏WebSocket错误页面
    if (showFullScreen && context != null && onRetry != null) {
      showWebSocketErrorPage(context, onRetry: onRetry, errorMessage: displayMessage);
    }
  }
  
  /// 处理业务逻辑错误
  static void handleBusinessError(
    String message, {
    dynamic error,
    bool showSnackbar = true,
    String errorCode = ErrorCodes.BUSINESS_ERROR,
  }) {
    // 创建业务异常对象
    final businessException = AppException(
      message,
      code: errorCode,
      originalError: error
    );
    
    // 使用统一的handleException处理错误
    handleException(
      businessException,
      message: message,
      showSnackbar: showSnackbar,
    );
  }
  
  /// 显示成功提示
  static void showSuccess(
    String message, {
    String? title,
    Duration? duration,
  }) {
    ToastUtil.success(message);
  }
  
  /// 显示信息提示
  static void showInfo(
    String message, {
    String? title,
    Duration? duration,
  }) {
    ToastUtil.info(message);
  }
  
  /// 显示全屏网络错误页面
  static void showNetworkErrorPage(
    BuildContext context, {
    required VoidCallback onRetry,
    String errorMessage = 'Network connection error',
    String buttonText = 'Try again',
    bool barrierDismissible = false,
  }) {
    _showFullScreenErrorDialog(
      context,
      onRetry: onRetry,
      errorMessage: errorMessage,
      buttonText: buttonText,
      barrierDismissible: barrierDismissible,
    );
  }
  
  /// 显示全屏WebSocket错误页面
  static void showWebSocketErrorPage(
    BuildContext context, {
    required VoidCallback onRetry,
    String errorMessage = 'WebSocket connection failed',
    String buttonText = 'Try again',
    bool barrierDismissible = false,
  }) {
    _showFullScreenErrorDialog(
      context,
      onRetry: onRetry,
      errorMessage: errorMessage,
      buttonText: buttonText,
      barrierDismissible: barrierDismissible,
    );
  }
  
  /// 显示全屏错误对话框的内部方法
  static void _showFullScreenErrorDialog(
    BuildContext context, {
    required VoidCallback onRetry,
    required String errorMessage,
    required String buttonText,
    bool barrierDismissible = false,
  }) {
    // 检查是否在对话框中
    if (Get.isDialogOpen ?? false) {
      Get.back();
    }
    
    showDialog(
      context: context,
      barrierDismissible: barrierDismissible,
      barrierColor: Colors.black.withOpacity(0.9),
      builder: (context) => WillPopScope(
        onWillPop: () async => barrierDismissible,
        child: NetworkErrorView(
          onRetry: () {
            Navigator.of(context).pop();
            onRetry();
          },
          errorMessage: errorMessage,
          buttonText: buttonText,
        ),
      ),
    );
  }
  
  /// 统一的异常处理入口
  /// 根据异常类型自动选择合适的处理方式
  static void handleException(
    dynamic exception, {
    String? message,
    bool showSnackbar = true,
    VoidCallback? onRetry,
  }) {
    try {
      String errorCode = ErrorCodes.GENERAL_ERROR;
      String displayMessage = message ?? MessageConstants.unknownError;
      String logPrefix = '';
      
      // 根据异常类型确定错误代码和显示消息
      if (exception is ApiException) {
        logPrefix = 'API错误';
        errorCode = exception.code.toString();
        displayMessage = message ?? exception.message;
      } else if (exception is NetworkException) {
        logPrefix = '网络错误';
        errorCode = ErrorCodes.NETWORK_ERROR;
        displayMessage = message ?? getMessageForErrorCode(errorCode);
      } else if (exception is AuthException) {
        logPrefix = '认证错误';
        errorCode = ErrorCodes.AUTH_ERROR;
        displayMessage = message ?? getMessageForErrorCode(errorCode);
      } else if (exception is ServerException) {
        logPrefix = '服务器错误';
        errorCode = ErrorCodes.SERVER_ERROR;
        displayMessage = message ?? getMessageForErrorCode(errorCode);
      } else if (exception is DataException) {
        logPrefix = '数据错误';
        errorCode = ErrorCodes.DATA_PARSE_ERROR;
        displayMessage = message ?? getMessageForErrorCode(errorCode);
      } else if (exception is WebSocketException) {
        logPrefix = 'WebSocket错误';
        errorCode = ErrorCodes.WS_CONNECTION_FAILED;
        displayMessage = message ?? getMessageForErrorCode(errorCode);
      } else if (exception is AppException) {
        logPrefix = '应用错误';
        errorCode = exception.code ?? ErrorCodes.GENERAL_ERROR;
        displayMessage = message ?? exception.message;
      }
      
      // 记录错误日志
      LogUtil.error('$logPrefix [$errorCode]: $displayMessage, error: $exception');
      
      // 直接显示错误消息，避免循环调用
      if (showSnackbar) {
        ToastUtil.error(displayMessage);
        
        // 如果有重试按钮，我们需要额外处理，因为ToastUtil不直接支持按钮
        if (onRetry != null) {
          // 使用延迟执行，避免两个提示框同时显示
          Future.delayed(const Duration(milliseconds: 3000), () {
            ToastUtil.showToast(
              MessageConstants.retryLoadingMessage,
              type: ToastType.warning,
              duration: const Duration(seconds: 2),
            );
          });
        }
      }
    } catch (e) {
      // 确保异常处理本身不会导致应用崩溃
      LogUtil.error('处理异常时发生错误: $e, 原始异常: $exception');
      
      // 显示简单的错误提示
      if (showSnackbar) {
        ToastUtil.error('An unexpected error occurred');
      }
    }
  }
}