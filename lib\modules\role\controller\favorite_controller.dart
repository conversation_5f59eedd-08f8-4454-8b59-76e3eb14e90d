import 'dart:async';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:rolio/common/event/event_bus.dart';
import 'package:rolio/common/interfaces/chat_service_interface.dart';
import 'package:rolio/common/models/ai_role.dart';
import 'package:rolio/common/utils/error_handler.dart';
import 'package:rolio/common/utils/logger.dart';
import 'package:rolio/common/utils/image_preloader.dart';
import 'package:rolio/manager/global_state.dart';
import 'package:rolio/modules/role/service/role_service.dart';

/// 收藏角色控制器
/// 负责管理收藏角色页面的UI状态和业务逻辑
class FavoriteController extends GetxController {
  // 服务和管理器 - 通过GetX获取
  late final RoleService roleService;
  IChatService? chatService;
  late final GlobalState globalState;
  
  // 收藏角色列表
  final RxList<AiRole> favoriteRoles = <AiRole>[].obs;
  
  // 加载状态
  final RxBool isLoading = false.obs;
  
  // 是否显示骨架屏 - 控制UI显示
  final RxBool showSkeleton = true.obs;
  
  // 图片预加载器
  final ImagePreloader _imagePreloader = ImagePreloader();
  
  // 分页相关
  final int pageSize = 10; // 每页加载数量
  int _currentPage = 1;
  int _totalItems = 0;
  int _totalPages = 0;
  
  // 分页状态
  final RxBool isLoadingMore = false.obs;
  final RxBool hasMoreData = true.obs;
  
  // 最后一次刷新时间戳，防止频繁刷新
  int _lastRefreshTime = 0;
  static const int MIN_REFRESH_INTERVAL = 5000; // 5秒
  
  // 首次加载标志
  bool _isFirstLoad = true;
  
  // 事件订阅
  StreamSubscription? _roleUpdatedSubscription;
  
  // 图片预加载器待清理URL列表
  final Set<String> _preloadedImageUrls = <String>{};
  
  @override
  void onInit() {
    super.onInit();
    
    // 通过GetX获取依赖
    roleService = Get.find<RoleService>();
    globalState = Get.find<GlobalState>();
    
    // 尝试获取ChatService，如果不可用不阻止初始化
    try {
      if (Get.isRegistered<IChatService>()) {
        chatService = Get.find<IChatService>();
        LogUtil.debug('FavoriteController: 已获取IChatService');
      } else {
        LogUtil.warn('FavoriteController: IChatService未注册，聊天功能可能不可用');
      }
    } catch (e) {
      LogUtil.warn('FavoriteController: 获取IChatService失败: $e');
    }
    
    // 监听角色更新事件
    _setupRoleUpdateListener();
  }
  
  /// 设置角色更新监听器
  void _setupRoleUpdateListener() {
    _roleUpdatedSubscription = EventBus().on(AppEvent.roleFavoriteStatusChanged).listen((event) {
      if (event != null && event is Map<String, dynamic>) {
        final roleId = event['data']['role_id'];
        final isFavorited = event['data']['is_favorited'];
        
        LogUtil.debug('收到角色收藏状态变更事件: roleId=$roleId, isFavorited=$isFavorited');
        
        // 安全检查，确保roleId和isFavorited不为空
        if (roleId != null) {
          if (isFavorited == false) {
            // 如果取消收藏，立即从列表中移除
            _removeRoleFromList(roleId);
            LogUtil.debug('已从收藏列表中移除角色: $roleId');
          } else if (isFavorited == true) {
            // 如果添加收藏，刷新列表
            refreshFavoriteRoles();
          }
        }
      }
    });
  }
  
  /// 更新角色收藏状态（不移除）
  void _updateRoleFavoriteStatus(int roleId, bool isFavorited) {
    final index = favoriteRoles.indexWhere((role) => role.id == roleId);
    if (index != -1) {
      // 更新角色的收藏状态，但不从列表中移除
      favoriteRoles[index] = favoriteRoles[index].copyWith(
        isFavorited: isFavorited
      );
      update(); // 通知UI更新
    }
  }
  
  /// 从列表中移除角色
  void _removeRoleFromList(int roleId) {
    final index = favoriteRoles.indexWhere((role) => role.id == roleId);
    if (index != -1) {
      favoriteRoles.removeAt(index);
      _totalItems -= 1;
      update();
    }
  }
  
  /// 加载收藏角色列表
  /// 
  /// [forceRefresh] 是否强制刷新，忽略缓存
  Future<void> loadFavoriteRoles({bool forceRefresh = false}) async {
    try {
      // 如果正在加载，不重复请求
      if (isLoading.value && !forceRefresh) return;
      
      // 重置分页
      _currentPage = 1;
      hasMoreData.value = true;
      
      // 设置加载状态
      isLoading.value = true;
      
      LogUtil.debug('加载收藏角色列表，页码: $_currentPage，每页数量: $pageSize');
      
      // 调用服务获取收藏角色
      final result = await roleService.getFavoritedRoles(page: _currentPage, size: pageSize);
      
      final roles = result['items'] as List<AiRole>;
      _totalItems = result['total'] as int;
      _totalPages = result['pages'] as int;
      
      // 更新列表
      favoriteRoles.clear();
      favoriteRoles.addAll(roles);
      
      // 判断是否有更多数据
      hasMoreData.value = _currentPage < _totalPages;
      
      // 数据加载完成后，预加载图片
      if (roles.isNotEmpty) {
        _preloadRoleImages(roles);
      }
      
      // 更新刷新时间
      _lastRefreshTime = DateTime.now().millisecondsSinceEpoch;
      
      // 数据加载完成后，无论列表是否为空都隐藏骨架屏
      if (showSkeleton.value) {
        Future.delayed(const Duration(milliseconds: 300), () {
          showSkeleton.value = false;
        });
      }
      
      // 非首次加载标志
      _isFirstLoad = false;
      
      LogUtil.debug('收藏角色列表加载完成，共${roles.length}个角色，总共$_totalItems个');
      
    } catch (e) {
      LogUtil.error('加载收藏角色列表失败: $e');
      ErrorHandler.handleException(e);
    } finally {
      isLoading.value = false;
    }
  }
  
  /// 刷新收藏角色列表
  /// 
  /// 用于下拉刷新
  Future<void> refreshFavoriteRoles() async {
    // 检查是否达到刷新间隔时间
    final now = DateTime.now().millisecondsSinceEpoch;
    if (now - _lastRefreshTime < MIN_REFRESH_INTERVAL) {
      LogUtil.debug('刷新间隔太短，跳过刷新');
      return;
    }
    
    await loadFavoriteRoles(forceRefresh: true);
  }
  
  /// 加载更多收藏角色
  Future<void> loadMoreFavoriteRoles() async {
    try {
      // 如果没有更多数据或者正在加载中，不执行加载
      if (!hasMoreData.value || isLoadingMore.value || isLoading.value) {
        return;
      }
      
      // 设置加载更多状态
      isLoadingMore.value = true;
      
      // 页码加1
      _currentPage++;
      
      LogUtil.debug('加载更多收藏角色，页码: $_currentPage，每页数量: $pageSize');
      
      // 获取下一页数据
      final result = await roleService.getFavoritedRoles(page: _currentPage, size: pageSize);
      
      final roles = result['items'] as List<AiRole>;
      _totalItems = result['total'] as int;
      _totalPages = result['pages'] as int;
      
      // 添加到列表
      favoriteRoles.addAll(roles);
      
      // 判断是否有更多数据
      hasMoreData.value = _currentPage < _totalPages;
      
      // 预加载新加载角色的图片
      if (roles.isNotEmpty) {
        _preloadRoleImages(roles);
      }
      
      LogUtil.debug('加载更多收藏角色完成，本次加载${roles.length}个，总共${favoriteRoles.length}个');
      
    } catch (e) {
      LogUtil.error('加载更多收藏角色失败: $e');
      ErrorHandler.handleException(e);
    } finally {
      isLoadingMore.value = false;
    }
  }
  
  /// 处理滚动通知，用于滚动到底部时加载更多
  bool handleScrollNotification(ScrollNotification notification) {
    if (notification is ScrollEndNotification) {
      if (notification.metrics.pixels >= notification.metrics.maxScrollExtent - 200) {
        loadMoreFavoriteRoles();
      }
    }
    return false;
  }
  
  /// 获取角色列表
  /// 
  /// 用于UI绑定
  List<AiRole> getRoles() {
    return favoriteRoles.toList();
  }
  
  /// 预加载角色图片
  void _preloadRoleImages(List<AiRole> roles) {
    for (final role in roles) {
      if (role.avatarUrl.isNotEmpty) {
        _imagePreloader.preloadImage(role.avatarUrl);
        // 记录预加载的URL，方便后续清理
        _preloadedImageUrls.add(role.avatarUrl);
      }
      if (role.coverUrl.isNotEmpty) {
        _imagePreloader.preloadImage(role.coverUrl);
        // 记录预加载的URL，方便后续清理
        _preloadedImageUrls.add(role.coverUrl);
      }
    }
  }
  
  /// 切换角色收藏状态
  Future<void> toggleFavorite(AiRole role) async {
    try {
      LogUtil.debug('切换角色收藏状态: ${role.name} (ID: ${role.id})');
      
      // 保存原始状态，用于错误回滚
      final originalStatus = role.isFavorited;
      
      // 乐观更新：先在UI上更新状态
      _updateRoleFavoriteStatus(role.id, !originalStatus);
      
      // 调用服务切换收藏状态
      final serverState = await roleService.toggleFavorite(role.id);
      
      if (serverState == null) {
        // 服务器操作失败，回滚UI状态
        LogUtil.warn('服务器操作失败，回滚UI状态: ${role.id}');
        _updateRoleFavoriteStatus(role.id, originalStatus);
        return;
      }
      
      // 如果服务器返回的状态与本地预期不一致，同步为服务器状态
      if (serverState != !originalStatus) {
        LogUtil.warn('服务器状态与预期不一致，同步为服务器状态: ${role.id}');
        _updateRoleFavoriteStatus(role.id, serverState);
      }
      
      // 如果是取消收藏状态且确认成功，从列表中移除
      if (!serverState) {
        _removeRoleFromList(role.id);
      }
      
      // 发送状态变更事件
      EventBus().fire(AppEvent.roleFavoriteStatusChanged, {
        'role_id': role.id,
        'is_favorited': serverState,
      });
    } catch (e) {
      LogUtil.error('切换角色收藏状态失败: $e');
      ErrorHandler.handleException(e);
      
      // 发生错误时，尝试获取角色真实状态
      try {
        final freshRole = await roleService.getRoleDetail(role.id);
        if (freshRole != null) {
          _updateRoleFavoriteStatus(role.id, freshRole.isFavorited);
          
          // 如果确认不是收藏状态，从列表中移除
          if (!freshRole.isFavorited) {
            _removeRoleFromList(role.id);
          }
          
          // 发送状态变更事件保持数据一致性
          EventBus().fire(AppEvent.roleFavoriteStatusChanged, {
            'role_id': role.id,
            'is_favorited': freshRole.isFavorited,
          });
        } else {
          // 如果无法获取最新状态，恢复原状态
          _updateRoleFavoriteStatus(role.id, role.isFavorited);
        }
      } catch (_) {
        // 如果刷新失败，只能使用之前的状态
        _updateRoleFavoriteStatus(role.id, role.isFavorited);
      }
    }
  }
  
  /// 检查更新
  /// 当应用恢复到前台或页面重新可见时调用
  Future<void> checkForUpdates() async {
    // 检查是否需要刷新
    final now = DateTime.now().millisecondsSinceEpoch;
    if (now - _lastRefreshTime > MIN_REFRESH_INTERVAL) {
      LogUtil.debug('页面恢复可见，刷新收藏角色列表');
      await refreshFavoriteRoles();
    }
  }
  
  @override
  void onClose() {
    // 取消事件订阅
    _roleUpdatedSubscription?.cancel();
    
    // 清理预加载的图片资源
    if (_preloadedImageUrls.isNotEmpty) {
      LogUtil.debug('清理预加载的图片资源: ${_preloadedImageUrls.length}张');
      // 实际清理预加载资源
      _imagePreloader.clearMemoryCache();
      _preloadedImageUrls.clear();
    }
    
    // 记录日志
    LogUtil.debug('FavoriteController: 已取消所有事件订阅并清理资源');
    
    super.onClose();
  }
} 