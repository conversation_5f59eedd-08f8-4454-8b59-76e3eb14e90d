import 'package:flutter/material.dart';
import 'package:rolio/common/constants/string_constants.dart';
import 'package:rolio/widgets/skeleton/shimmer_widget.dart';
import 'package:rolio/widgets/skeleton/skeleton_container.dart';

/// 推荐页面的骨架屏组件
/// 
/// 在数据加载过程中显示骨架屏，提供更好的加载体验
class SkeletonRecommendGrid extends StatelessWidget {
  const SkeletonRecommendGrid({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return CustomScrollView(
      physics: const NeverScrollableScrollPhysics(),
      slivers: [
        // 顶部间距
        const SliverToBoxAdapter(
          child: SizedBox(height: 12),
        ),
      
        // 网格骨架
        SliverPadding(
          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
          sliver: SliverLayoutBuilder(
            builder: (context, constraints) {
              // 计算最佳列数
              final crossAxisCount = _calculateOptimalColumnCount(constraints.crossAxisExtent);
              
              return SliverGrid(
                gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                  crossAxisCount: crossAxisCount,
                  childAspectRatio: StringsConsts.recommendCardAspectRatio,
                  crossAxisSpacing: StringsConsts.recommendGridSpacing,
                  mainAxisSpacing: StringsConsts.recommendGridSpacing,
                ),
                delegate: SliverChildBuilderDelegate(
                  (context, index) => _buildSkeletonCard(),
                  childCount: 6, // 显示6个骨架卡片
                ),
              );
            },
          ),
        ),
      ],
    );
  }

  /// 构建骨架卡片
  Widget _buildSkeletonCard() {
    return ShimmerWidget(
      child: Container(
        decoration: BoxDecoration(
          color: Colors.grey[800],
          borderRadius: BorderRadius.circular(StringsConsts.recommendCardBorderRadius),
        ),
        child: Stack(
          children: [
            // 卡片背景
            Container(
              decoration: BoxDecoration(
                color: Colors.grey[850],
                borderRadius: BorderRadius.circular(StringsConsts.recommendCardBorderRadius),
              ),
            ),
            
            // 底部内容区域
            Positioned(
              bottom: 0,
              left: 0,
              right: 0,
              child: Container(
                padding: const EdgeInsets.all(12.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // 头像和标题行
                    Row(
                      children: [
                        // 头像骨架
                        SkeletonContainer(
                          width: 32,
                          height: 32,
                          borderRadius: 16,
                        ),
                        const SizedBox(width: 8),
                        // 标题骨架
                        Expanded(
                          child: SkeletonContainer(
                            height: 18,
                            borderRadius: 4,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 8),
                    // 描述骨架 - 四行
                    SkeletonContainer(
                      height: 12,
                      width: double.infinity,
                      borderRadius: 4,
                    ),
                    const SizedBox(height: 4),
                    SkeletonContainer(
                      height: 12,
                      width: double.infinity,
                      borderRadius: 4,
                    ),
                    const SizedBox(height: 4),
                    SkeletonContainer(
                      height: 12,
                      width: double.infinity,
                      borderRadius: 4,
                    ),
                    const SizedBox(height: 4),
                    SkeletonContainer(
                      height: 12,
                      width: 160,
                      borderRadius: 4,
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
  
  /// 根据屏幕宽度计算最佳列数
  int _calculateOptimalColumnCount(double width) {
    if (width < 600) {
      return 2; // 手机屏幕
    } else if (width < 900) {
      return 3; // 小平板
    } else if (width < 1200) {
      return 4; // 大平板
    } else {
      return 5; // 桌面
    }
  }
} 