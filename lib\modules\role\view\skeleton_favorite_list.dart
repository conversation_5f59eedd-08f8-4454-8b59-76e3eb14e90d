import 'package:flutter/material.dart';
import 'package:rolio/widgets/skeleton/shimmer_widget.dart';
import 'package:rolio/widgets/skeleton/skeleton_container.dart';

/// 收藏角色页面的列表骨架屏组件
/// 
/// 在数据加载过程中显示列表骨架屏，提供更好的加载体验
class SkeletonFavoriteList extends StatelessWidget {
  const SkeletonFavoriteList({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return CustomScrollView(
      physics: const NeverScrollableScrollPhysics(),
      slivers: [
        // 顶部间距
        const SliverToBoxAdapter(
          child: SizedBox(height: 8),
        ),
      
        // 列表骨架
        SliverPadding(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          sliver: SliverList(
            delegate: SliverChildBuilderDelegate(
              (context, index) => Padding(
                padding: const EdgeInsets.only(bottom: 12),
                child: _buildSkeletonListItem(),
              ),
              childCount: 8, // 显示8个骨架列表项
            ),
          ),
        ),
      ],
    );
  }

  /// 构建骨架列表项
  Widget _buildSkeletonListItem() {
    return ShimmerWidget(
      child: Container(
        height: 84,
        decoration: BoxDecoration(
          color: const Color(0xFF1A1A1A),
          borderRadius: BorderRadius.circular(16.0),
        ),
        padding: const EdgeInsets.all(12),
        child: Row(
          children: [
            // 头像骨架
            const SkeletonContainer(
              width: 60,
              height: 60,
              borderRadius: 16,
            ),
            
            const SizedBox(width: 12),
            
            // 角色信息骨架
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  // 名字骨架
                  const SkeletonContainer(
                    height: 16,
                    width: 120,
                    borderRadius: 4,
                  ),
                  
                  const SizedBox(height: 8),
                  
                  // 描述骨架 - 两行
                  const SkeletonContainer(
                    height: 12,
                    width: double.infinity,
                    borderRadius: 4,
                  ),
                  const SizedBox(height: 6),
                  const SkeletonContainer(
                    height: 12,
                    width: 180, // 固定宽度替代 MediaQuery
                    borderRadius: 4,
                  ),
                ],
              ),
            ),
            
            const SizedBox(width: 8),
            
            // 右侧箭头占位
            const SkeletonContainer(
              width: 16,
              height: 16,
              borderRadius: 4,
            ),
          ],
        ),
      ),
    );
  }
} 