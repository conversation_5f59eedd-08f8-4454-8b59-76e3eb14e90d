import 'dart:async';
import 'package:get/get.dart';
import 'package:rolio/common/utils/logger.dart';
import 'package:rolio/common/utils/error_handler.dart';
import 'package:rolio/common/utils/data_converter_util.dart';
import 'package:rolio/manager/global_state.dart';
import 'package:rolio/manager/ws_manager.dart';
import 'package:rolio/manager/ws_connection_manager.dart';
import 'package:rolio/manager/ws_message_manager.dart';
import 'package:rolio/common/cache/cache_manager.dart';
import 'package:rolio/common/cache/secure_storage.dart';
import 'package:rolio/common/event/event_bus.dart';
import 'package:rolio/common/services/cache_cleanup_service.dart';
import 'package:rolio/common/services/role_provider.dart';
import 'package:rolio/common/services/session_binding_service.dart';
import 'package:rolio/modules/login/repository/login_repository.dart';
import 'package:rolio/modules/login/service/login_service.dart';
import 'package:rolio/common/interfaces/role_provider.dart';
import 'package:rolio/common/services/interface_providers.dart';
import 'package:rolio/common/utils/message_tracker.dart';
import 'package:rolio/common/di/service_bindings.dart';

/// 应用依赖注入器
///
/// 实现GetX的Bindings接口，统一管理所有全局依赖
/// 同时保留原有API，确保兼容性
class AppBinding extends Bindings {
  // 标记是否已初始化
  static bool _isInitialized = false;
  
  @override
  void dependencies() {
    if (_isInitialized) {
      LogUtil.warn('AppBinding已经初始化，跳过重复初始化');
      return;
    }
    
    try {
      LogUtil.info('开始注册全局基础依赖...');
      
      // 1. 注册基础工具和服务
      _registerBaseServices();
      
      // 2. 注册网络相关服务
      _registerNetworkServices();
      
      // 3. 注册业务服务
      _registerBusinessServices();
      
      // 4. 注册认证服务
      _registerAuthServices();
      
      _isInitialized = true;
      LogUtil.info('全局基础依赖注册完成');
    } catch (e) {
      LogUtil.error('注册全局依赖失败: $e');
      rethrow;
    }
  }
  
  /// 注册基础工具和服务
  void _registerBaseServices() {
    try {
      // 安全存储服务（在全局状态之前注册）
      Get.put<SecureStorage>(SecureStorage(), permanent: true);
      LogUtil.debug('已注册SecureStorage安全存储服务');
      
      // 全局状态管理
      Get.put<GlobalState>(GlobalState(), permanent: true);
      LogUtil.debug('已注册GlobalState全局状态');
      
      // 基础工具类
      Get.put<ErrorHandler>(ErrorHandler(), permanent: true);
      Get.put<CacheManager>(CacheManager.getInstance(), permanent: true);
      Get.put<DataConverter>(DataConverter(), permanent: true);
      LogUtil.debug('已注册基础工具类');
      
      // 事件总线
      Get.put<EventBus>(EventBus(), permanent: true);
      LogUtil.debug('已注册EventBus事件总线');
      
      // 缓存清理服务
      Get.put<CacheCleanupService>(CacheCleanupService.getInstance(), permanent: true);
      LogUtil.debug('已注册CacheCleanupService缓存清理服务');
      
      // 消息跟踪器
      Get.put<MessageTracker>(MessageTracker(), permanent: true);
      LogUtil.debug('已注册MessageTracker消息跟踪器');
      
      LogUtil.debug('基础服务注册完成');
    } catch (e) {
      LogUtil.error('注册基础服务失败: $e，堆栈: ${StackTrace.current}');
      rethrow;
    }
  }
  
  /// 注册网络相关服务
  void _registerNetworkServices() {
    try {
      // 按照正确的依赖顺序注册
      
      // 1. 先注册WsConnectionManager
      WsConnectionManager connectionManager = WsConnectionManager();
      if (!Get.isRegistered<WsConnectionManager>()) {
        Get.put<WsConnectionManager>(connectionManager, permanent: true);
        LogUtil.debug('已注册WsConnectionManager');
      } else {
        connectionManager = Get.find<WsConnectionManager>();
        LogUtil.debug('WsConnectionManager已注册，使用现有实例');
      }
      
      // 2. 再注册WsMessageManager，它依赖于WsConnectionManager
      if (!Get.isRegistered<WsMessageManager>()) {
        final messageManager = WsMessageManager();
        Get.put<WsMessageManager>(messageManager, permanent: true);
        LogUtil.debug('已注册WsMessageManager');
      } else {
        LogUtil.debug('WsMessageManager已注册，跳过重复注册');
      }
      
      // 3. 最后注册WsManager，它依赖前两者
      if (!Get.isRegistered<WsManager>()) {
        final wsManager = WsManager();
        Get.put<WsManager>(wsManager, permanent: true);
        LogUtil.debug('已注册WsManager');
      } else {
        LogUtil.debug('WsManager已注册，跳过重复注册');
      }
      
      LogUtil.debug('网络相关服务注册完成');
    } catch (e) {
      LogUtil.error('注册网络服务失败: $e，堆栈: ${StackTrace.current}');
      rethrow;
    }
  }
  
  /// 注册业务服务
  void _registerBusinessServices() {
    try {
      // 注册服务依赖
      final serviceBindings = ServiceBindings();
      serviceBindings.dependencies();
      LogUtil.debug('已通过ServiceBindings注册业务服务');
      
      // 会话绑定服务
      Get.put<SessionBindingService>(
        SessionBindingService(),
        permanent: true,
      );
      LogUtil.debug('已注册SessionBindingService会话绑定服务');
      
      LogUtil.debug('业务服务注册完成');
    } catch (e) {
      LogUtil.error('注册业务服务失败: $e，堆栈: ${StackTrace.current}');
      rethrow;
    }
  }
  
  /// 注册认证相关服务
  void _registerAuthServices() {
    try {
      // 注册登录仓库
      Get.put<LoginRepository>(LoginRepository(), permanent: false);
      
      // 注册登录服务
      Get.put<LoginService>(
        LoginService(repository: Get.find<LoginRepository>()),
        permanent: true,
      );
      
      LogUtil.debug('认证服务注册完成');
    } catch (e) {
      LogUtil.error('注册认证服务失败: $e，堆栈: ${StackTrace.current}');
      rethrow;
    }
  }
  
  /// 简化的初始化方法
  static Future<void> init() async {
    if (_isInitialized) {
      LogUtil.warn('AppBinding已经初始化，跳过重复初始化');
      return;
    }
    
    try {
      LogUtil.info('开始初始化全局依赖...');
      
      final binding = AppBinding();
      binding.dependencies();
      
      LogUtil.info('全局依赖初始化完成');
    } catch (e) {
      LogUtil.error('初始化全局依赖失败: $e');
      rethrow;
    }
  }
  
  // 保留兼容性API
  
  /// 检查服务是否已注册
  static bool isRegistered<T>({String? tag}) {
    return Get.isRegistered<T>(tag: tag);
      }
      
  /// 获取服务实例
  static T resolve<T>({String? tag}) {
    return Get.find<T>(tag: tag);
  }

  /// 注册单例服务
  static void registerSingleton<T>(
    T instance, {
    String? tag,
    bool permanent = true,
  }) {
    try {
      // 检查是否已注册
      if (isRegistered<T>(tag: tag)) {
        LogUtil.debug('服务已注册: ${T.toString()}${tag != null ? " (tag: $tag)" : ""}');
        return;
      }
      
      Get.put<T>(instance, tag: tag, permanent: permanent);
      LogUtil.debug('注册服务: ${T.toString()}${tag != null ? " (tag: $tag)" : ""}');
    } catch (e) {
      LogUtil.error('注册服务失败: ${T.toString()}${tag != null ? " (tag: $tag)" : ""}, 错误: $e');
      rethrow;
    }
  }
  
  /// 注册懒加载单例服务
  static void registerLazySingleton<T>(
    T Function() builder, {
    String? tag,
    bool permanent = true,
  }) {
    try {
      Get.lazyPut<T>(builder, tag: tag, fenix: permanent);
      LogUtil.debug('注册懒加载服务: ${T.toString()}${tag != null ? " (tag: $tag)" : ""}');
    } catch (e) {
      LogUtil.error('注册懒加载服务失败: ${T.toString()}${tag != null ? " (tag: $tag)" : ""}, 错误: $e');
      rethrow;
    }
  }
  
  /// 注册工厂服务（每次获取都创建新实例）
  static void registerFactory<T>(
    T Function() builder, {
    String? tag,
  }) {
    try {
      Get.create<T>(builder, tag: tag, permanent: false);
      LogUtil.debug('注册工厂服务: ${T.toString()}${tag != null ? " (tag: $tag)" : ""}');
    } catch (e) {
      LogUtil.error('注册工厂服务失败: ${T.toString()}${tag != null ? " (tag: $tag)" : ""}, 错误: $e');
      rethrow;
    }
  }
}