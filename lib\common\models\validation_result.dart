/// 数据验证结果模型
/// 
/// 用于标准化数据验证结果，提高类型安全性
class ValidationResult {
  /// 是否验证通过
  final bool isValid;
  
  /// 错误信息列表
  final List<ValidationError> errors;
  
  /// 构造函数
  const ValidationResult({
    required this.isValid,
    this.errors = const [],
  });
  
  /// 创建验证成功结果
  factory ValidationResult.success() {
    return const ValidationResult(isValid: true);
  }
  
  /// 创建验证失败结果
  factory ValidationResult.failure(List<ValidationError> errors) {
    return ValidationResult(isValid: false, errors: errors);
  }
  
  /// 创建单个错误的验证失败结果
  factory ValidationResult.error(String field, String message) {
    return ValidationResult(
      isValid: false,
      errors: [ValidationError(field: field, message: message)],
    );
  }
  
  /// 合并多个验证结果
  factory ValidationResult.merge(List<ValidationResult> results) {
    final allErrors = <ValidationError>[];
    for (final result in results) {
      allErrors.addAll(result.errors);
    }
    return ValidationResult(isValid: allErrors.isEmpty, errors: allErrors);
  }
  
  /// 获取错误信息
  String get errorMessage {
    if (isValid) return '';
    return errors.map((e) => e.message).join(', ');
  }
  
  /// 获取指定字段的错误信息
  String? getFieldError(String field) {
    if (isValid) return null;
    final error = errors.firstWhere(
      (e) => e.field == field,
      orElse: () => ValidationError(field: '', message: ''),
    );
    return error.field.isNotEmpty ? error.message : null;
  }
}

/// 验证错误模型
class ValidationError {
  /// 错误字段名
  final String field;
  
  /// 错误信息
  final String message;
  
  /// 构造函数
  const ValidationError({
    required this.field,
    required this.message,
  });
} 