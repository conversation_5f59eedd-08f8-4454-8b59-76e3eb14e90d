import 'dart:async';
import 'package:get/get.dart';
import 'package:rolio/common/interfaces/session_provider.dart';
import 'package:rolio/common/utils/logger.dart';
import 'package:rolio/manager/global_state.dart';
import 'package:rolio/modules/sessions/model/session.dart';
import 'package:rolio/modules/sessions/repository/sessions_repository.dart';
import 'package:rolio/common/constants/string_constants.dart';
import 'package:rolio/common/models/page_data.dart';
import 'package:rolio/common/models/page_request.dart';
import 'package:rolio/common/utils/error_handler.dart';
import 'package:rolio/common/event/event_bus.dart';
import 'package:rolio/modules/chat/service/ai_channel_manager.dart';
import 'package:rolio/manager/ws_manager.dart';
import 'package:rolio/common/services/role_provider.dart';

/// 会话服务，实现ISessionProvider接口
class SessionService extends GetxService implements ISessionProvider {
  // 全局状态
  final GlobalState _globalState;
  
  // 会话仓库
  final SessionsRepository _repository;
  
  // 会话列表
  final RxList<Session> sessions = <Session>[].obs;
  
  // 分页数据
  final Rx<PageData<Session>> _pageData = PageData<Session>.empty().obs;
  
  // 分页数据访问器
  PageData<Session> get pageData => _pageData.value;
  
  // 是否正在加载
  final RxBool isLoading = false.obs;
  
  // 是否需要刷新
  bool _needsRefresh = false;
  
  // 事件总线
  late final EventBus _eventBus;
  
  // 事件订阅
  StreamSubscription? _sessionUpdatedSubscription;
  
  // 构造函数，支持依赖注入
  SessionService({
    required SessionsRepository repository,
    required GlobalState globalState,
  }) : _repository = repository,
       _globalState = globalState {
    _eventBus = Get.find<EventBus>();
    _setupEventListeners();
  }
  
  // 设置事件监听
  void _setupEventListeners() {
    _sessionUpdatedSubscription = _eventBus.on(AppEvent.sessionUpdated).listen((event) {
      final data = event['data'] as Map<String, dynamic>;
      updateSessionOrder(
        data['conversationId'] as int,
        data['lastMessage'] as String?,
        data['lastMessageTime'] as DateTime?,
      );
    });
  }
  
  @override
  void onInit() {
    super.onInit();
    LogUtil.debug('SessionService初始化');
  }
  
  @override
  void onClose() {
    _sessionUpdatedSubscription?.cancel();
    super.onClose();
  }
  
  /// 更新会话顺序
  /// 
  /// [conversationId] 会话ID
  /// [lastMessage] 最新消息内容
  /// [lastMessageTime] 最新消息时间
  void updateSessionOrder(int conversationId, String? lastMessage, DateTime? lastMessageTime) {
    try {
      if (sessions.isEmpty) {
        LogUtil.debug('会话列表为空，跳过静默更新');
        return;
      }
      
      if (conversationId <= 0) {
        LogUtil.debug('无效会话ID，跳过静默更新');
        return;
      }
      
      // 查找对应的会话并更新时间戳和最新消息
      final sessionIndex = sessions.indexWhere((s) => s.id == conversationId);
      if (sessionIndex != -1) {
        final currentSession = sessions[sessionIndex];
        final now = DateTime.now();
        
        // 创建更新后的会话对象，同时更新时间和最新消息
        final updatedSession = currentSession.copyWith(
          lastMessage: lastMessage ?? currentSession.lastMessage,
          lastMessageCreatedAt: lastMessageTime ?? now,
          updatedAt: now,
        );
        
        // 更新会话列表中的对应项
        sessions[sessionIndex] = updatedSession;
        
        // 移除客户端排序，本地更新后需要刷新数据
        refreshSessions();
        
        LogUtil.debug('静默更新会话成功: 会话ID=$conversationId, 最新消息: $lastMessage');
      } else {
        LogUtil.debug('未找到会话ID=$conversationId，刷新会话列表');
        refreshSessions();
      }
    } catch (e) {
      LogUtil.error('更新会话顺序失败: $e');
      // 出错时刷新整个列表
      refreshSessions();
    }
  }
  
  /// 获取分页会话数据
  Future<PageData<Session>> getPagedSessions(PageRequest request, {bool forceRefresh = false}) async {
    try {
      isLoading.value = true;
      
      // 传递 forceRefresh 参数到仓库方法
      final result = await _repository.getPagedList(request, forceRefresh: forceRefresh);
      
      if (result.items.isNotEmpty) {
        if (request.page == 1) {
          // 第一页，替换整个列表
          sessions.clear();
          
          // 获取新会话列表并直接添加，不再进行客户端排序
          // 完全依赖服务端排序，解决客户端和服务端排序逻辑重复问题
          sessions.addAll(result.items);
        } else {
          // 不是第一页，添加到现有列表，并去重
          final newSessions = result.items.where(
            (newSession) => !sessions.any((s) => s.id == newSession.id)
          ).toList();
          
          if (newSessions.isNotEmpty) {
            sessions.addAll(newSessions);
            // 移除客户端排序逻辑，完全依赖服务端排序
          }
        }
        
        // 更新分页数据
        _pageData.value = result;
      } else {
        LogUtil.warn('加载会话列表为空');
      }
      
      return result;
    } catch (e) {
      LogUtil.error('加载会话列表异常: $e');
      // 统一使用ErrorHandler.handleException()方法
      ErrorHandler.handleException(e, showSnackbar: false);
      return PageData.empty();
    } finally {
      isLoading.value = false;
    }
  }
  
  // 新版刷新会话方法，替代旧版的refreshSessions
  @override
  Future<void> refreshSessions({int page = 1, int pageSize = StringsConsts.defaultPageSize, bool refresh = false}) async {
    LogUtil.debug('刷新会话列表: 页码=$page, 每页大小=$pageSize, 强制刷新=$refresh');
    
    final request = PageRequest(
      page: page,
      size: pageSize,
      sortBy: 'updated_at',
      sortDirection: SortDirection.desc,
    );
    
    if (refresh) {
      // 刷新时先清空现有列表
      sessions.clear();
      _pageData.value = PageData.empty();
      LogUtil.debug('刷新会话列表：已清空现有会话数据');
    }
    
    await getPagedSessions(request, forceRefresh: refresh);
  }
  
  /// 获取会话分页加载页码
  int get currentPage => _pageData.value.page;
  
  /// 刷新数据标志
  bool get needsRefresh => _needsRefresh;

  /// 加载会话列表（实现ISessionProvider接口）
  /// 
  /// [page] 页码
  /// [pageSize] 每页数量
  @override
  Future<void> loadSessions({int page = 1, int pageSize = 10}) async {
    LogUtil.debug('加载会话列表: 页码=$page, 每页大小=$pageSize');
    final request = PageRequest(
      page: page,
      size: pageSize,
      sortBy: 'updated_at',
      sortDirection: SortDirection.desc,
    );
    await getPagedSessions(request, forceRefresh: false);
  }
  
  /// 加载更多会话（实现ISessionProvider接口）
  /// 
  /// [page] 页码
  /// [pageSize] 每页数量
  @override
  Future<void> loadMoreSessions({required int page, required int pageSize}) async {
    LogUtil.debug('加载更多会话: 页码=$page, 每页大小=$pageSize');
    final request = PageRequest(
      page: page,
      size: pageSize,
      sortBy: 'updated_at',
      sortDirection: SortDirection.desc,
    );
    await getPagedSessions(request, forceRefresh: false);
  }

  /// 删除会话
  /// 
  /// [id] 要删除的会话ID
  /// 返回删除是否成功
  Future<bool> deleteSession(int id) async {
    try {
      LogUtil.debug('服务层 - 开始删除会话, ID=$id');
      
      final result = await _repository.delete(id);
      
      if (result) {
        // 更新状态
        sessions.removeWhere((session) => session.id == id);
        _needsRefresh = true;
        LogUtil.debug('服务层 - 会话删除成功，ID=$id');
        
        // 删除成功后触发清理相关资源
        await _clearRelatedRoleBindingsSync(id, null);
        await _clearSessionCacheSync(id);
      } else {
        LogUtil.warn('服务层 - 会话删除失败，ID=$id');
      }
      
      return result;
    } catch (e) {
      LogUtil.error('服务层 - 删除会话失败: $e');
      return false;
    }
  }
  
  /// 置顶会话
  Future<bool> pinSession(int id) async {
    try {
      LogUtil.debug('服务层 - 开始置顶会话, ID=$id');
      
      final result = await _repository.pinConversation(id);
      
      if (result) {
        // 更新状态
        _needsRefresh = true;
        LogUtil.debug('服务层 - 会话置顶成功，ID=$id');
      } else {
        LogUtil.warn('服务层 - 会话置顶失败，ID=$id');
      }
      
      return result;
    } catch (e) {
      LogUtil.error('服务层 - 置顶会话失败: $e');
      return false;
    }
  }
  
  /// 取消置顶会话
  Future<bool> unpinSession(int id) async {
    try {
      LogUtil.debug('服务层 - 开始取消置顶会话, ID=$id');
      
      final result = await _repository.unpinConversation(id);
      
      if (result) {
        // 更新状态
        _needsRefresh = true;
        LogUtil.debug('服务层 - 会话取消置顶成功，ID=$id');
      } else {
        LogUtil.warn('服务层 - 会话取消置顶失败，ID=$id');
      }
      
      return result;
    } catch (e) {
      LogUtil.error('服务层 - 取消置顶会话失败: $e');
      return false;
    }
  }
  
  /// 隐藏会话
  Future<bool> hideSession(int id) async {
    try {
      LogUtil.debug('服务层 - 开始隐藏会话, ID=$id');
      
      final result = await _repository.hideConversation(id);
      
      if (result) {
        // 更新状态 - 隐藏会话后，从列表中移除
        sessions.removeWhere((session) => session.id == id);
        _needsRefresh = true;
        LogUtil.debug('服务层 - 会话隐藏成功，ID=$id');
      } else {
        LogUtil.warn('服务层 - 会话隐藏失败，ID=$id');
      }
      
      return result;
    } catch (e) {
      LogUtil.error('服务层 - 隐藏会话失败: $e');
      return false;
    }
  }
  
  // 同步版本的角色绑定清理
  Future<void> _clearRelatedRoleBindingsSync(int conversationId, int? roleId) async {
    try {
      // 使用RoleProvider重置所有引用该会话的角色
      if (Get.isRegistered<RoleProvider>()) {
        final roleProvider = Get.find<RoleProvider>();
        final count = await roleProvider.resetRoleConversationIdByConversationId(conversationId);
        if (count > 0) {
          LogUtil.info('已重置 $count 个角色的会话引用，conversationId=$conversationId');
        } else {
          LogUtil.info('未找到引用会话ID=$conversationId的角色，无需重置');
        }
      }
      
      // 如果找到了角色ID，通过事件总线通知相关服务清理资源
      if (roleId != null && roleId > 0) {
        LogUtil.info('找到与会话$conversationId关联的角色ID: $roleId，开始清理相关资源');
        
        // 发送角色资源清理事件
        _eventBus.fire(AppEvent.roleResourceCleanup, {
          'roleId': roleId,
          'conversationId': conversationId,
          'isActiveRole': false,
          'reason': 'session_deleted'
        });
        
        // 通过事件总线通知SessionBindingService清理绑定关系
        _eventBus.fire(AppEvent.roleBindingCleanup, {
          'roleId': roleId,
          'conversationId': conversationId,
        });
        
        LogUtil.debug('已通知清理角色绑定关系: roleId=$roleId, conversationId=$conversationId');
      } else {
        // 如果没有找到特定角色，通过事件总线通知清理与会话相关的所有资源
        LogUtil.debug('未找到与会话$conversationId关联的具体角色，开始查找并清理');
        
        // 如果有AiChannelManager，找出所有与该会话相关的角色
        if (Get.isRegistered<AiChannelManager>()) {
          final channelManager = Get.find<AiChannelManager>();
          // 获取当前所有订阅角色
          final subscribedRoles = channelManager.getSubscribedRoleIds();
          
          // 检查是否存在订阅角色
          if (subscribedRoles.isEmpty) {
            LogUtil.debug('当前没有订阅的角色，跳过角色绑定清理');
            // 没有订阅角色时，直接发送会话资源清理事件
            _eventBus.fire(AppEvent.conversationResourceCleanup, {
              'conversationId': conversationId,
              'reason': 'session_deleted_no_roles'
            });
            return;
          }
          
          LogUtil.debug('找到 ${subscribedRoles.length} 个订阅角色，开始检查与会话 $conversationId 的关联');
          
          // 用于记录是否找到相关角色
          bool foundRelatedRole = false;
          
          // 查找与该会话相关的角色
          for (final roleId in subscribedRoles) {
            try {
              final roleConversationId = channelManager.getConversationIdForRole(roleId);
              if (roleConversationId == conversationId) {
                // 找到相关角色，通过事件清理其资源
                _eventBus.fire(AppEvent.roleResourceCleanup, {
                  'roleId': roleId,
                  'conversationId': conversationId,
                  'isActiveRole': false,
                  'reason': 'session_deleted'
                });
                
                // 清理角色绑定
                _eventBus.fire(AppEvent.roleBindingCleanup, {
                  'roleId': roleId,
                  'conversationId': conversationId,
                });
                
                LogUtil.debug('已通知清理相关角色绑定: roleId=$roleId, conversationId=$conversationId');
                foundRelatedRole = true;
              }
            } catch (e) {
              LogUtil.error('检查角色ID=$roleId与会话ID=$conversationId的关联时出错: $e');
            }
          }
          
          // 如果没有找到相关角色，记录日志
          if (!foundRelatedRole) {
            LogUtil.debug('未找到与会话ID=$conversationId相关的角色绑定，发送会话资源清理事件');
            // 发送会话资源清理事件
            _eventBus.fire(AppEvent.conversationResourceCleanup, {
              'conversationId': conversationId,
              'reason': 'session_deleted_no_role_bindings'
            });
          }
        } else {
          // 发送会话资源清理事件
          _eventBus.fire(AppEvent.conversationResourceCleanup, {
            'conversationId': conversationId,
            'reason': 'session_deleted'
          });
        }
      }
      
      LogUtil.debug('角色绑定清理完成: conversationId=$conversationId');
    } catch (e) {
      LogUtil.error('清理角色绑定关系失败: $e');
      // 即使出错，也发送会话资源清理事件
      _eventBus.fire(AppEvent.conversationResourceCleanup, {
        'conversationId': conversationId,
        'reason': 'session_deleted_error'
      });
    }
  }
  
  // 同步版本的缓存清理
  Future<void> _clearSessionCacheSync(int conversationId) async {
    try {
      LogUtil.info('开始清除会话$conversationId的所有相关缓存');
      
      // 通过事件总线发送会话缓存清理事件
      _eventBus.fire(AppEvent.sessionCacheCleanup, {
        'conversationId': conversationId,
        'reason': 'session_deleted'
      });
      
      // 清除WsManager中的会话通道
      if (Get.isRegistered<WsManager>()) {
        try {
          final wsManager = Get.find<WsManager>();
          wsManager.switchToGlobalChannel();
          LogUtil.debug('已将WsManager切换回全局通道');
        } catch (e) {
          LogUtil.error('切换WsManager通道失败: $e');
        }
      }
      
      LogUtil.debug('已通知清除会话相关的所有缓存: conversationId=$conversationId');
    } catch (e) {
      LogUtil.error('清除会话缓存失败: $e');
    }
  }
  
  // 清除缓存
  Future<void> clearCache() async {
    try {
      LogUtil.debug('清除会话服务缓存');
      await _repository.clearCache();
      sessions.clear();
      _pageData.value = PageData.empty();
    } catch (e) {
      LogUtil.error('清除会话缓存失败: $e');
      // 统一使用ErrorHandler.handleException()方法
      ErrorHandler.handleException(e, showSnackbar: false);
    }
  }
  
  // 实现ISessionProvider接口 - 获取会话流
  @override
  Stream<List<dynamic>> getSessions() {
    // 创建一个新的StreamController，避免直接使用RxList的stream
    // 这样可以避免在订阅时出现Future断言错误
    final controller = StreamController<List<dynamic>>();
    
    // 如果会话列表为空，先尝试加载
    if (sessions.isEmpty) {
      // 异步加载会话列表
      Future.microtask(() async {
        try {
          await refreshSessions(refresh: false);
          // 加载完成后，将会话列表添加到流中
          if (!controller.isClosed) {
            controller.add(sessions.toList());
          }
        } catch (e) {
          LogUtil.error('加载会话列表失败: $e');
          // 即使加载失败，也要返回空列表而不是错误
          if (!controller.isClosed) {
            controller.add([]);
          }
        } finally {
          // 完成后关闭控制器
          if (!controller.isClosed) {
            controller.close();
          }
        }
      });
    } else {
      // 如果会话列表已有数据，直接返回
      controller.add(sessions.toList());
      // 完成后关闭控制器
      Future.microtask(() {
        if (!controller.isClosed) {
          controller.close();
        }
      });
    }
    
    // 返回流
    return controller.stream;
  }
  
  // 获取分页数据流
  Stream<PageData<Session>> getPagedSessionsStream() {
    return _pageData.stream;
  }

  @override
  void updateLatestMessage({required int conversationId, required String latestMessage, required DateTime timestamp}) {
    // TODO: implement updateLatestMessage
    throw UnimplementedError();
  }
}