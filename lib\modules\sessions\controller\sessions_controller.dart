import 'dart:async';
import 'package:get/get.dart';
import 'package:rolio/common/interfaces/session_provider.dart';
import 'package:rolio/routes/routes.dart';
import 'package:rolio/common/utils/logger.dart';
import 'package:rolio/modules/sessions/model/session.dart';
import 'package:rolio/common/utils/http_manager_util.dart';
import 'package:rolio/common/constants/string_constants.dart';
import 'package:rolio/common/constants/message_constants.dart';
import 'package:rolio/common/utils/error_handler.dart';
import 'package:flutter/material.dart';
import 'package:rolio/common/event/event_bus.dart';
import 'package:rolio/common/models/page_request.dart';
import 'package:rolio/common/models/page_data.dart';
import 'package:rolio/common/utils/image_preloader.dart';
import 'package:rolio/routes/router_manager.dart';
import 'package:rolio/common/services/role_provider.dart';
import 'package:rolio/manager/global_state.dart';
import 'package:rolio/common/utils/toast_util.dart';

/// 会话控制器
///
/// 管理会话列表页面的UI状态和业务逻辑
class SessionsController extends GetxController {
  final ISessionProvider sessionService;
  final RoleProvider _roleService = Get.find<RoleProvider>();

  // 加载状态 - 代理服务层状态
  RxBool get isLoading => sessionService.isLoading;
  final RxBool isLoadingMore = false.obs;
  final RxString errorMessage = ''.obs;

  // 会话删除加载状态
  final RxBool isDeletingSession = false.obs;

  // 会话列表 - 代理服务层数据
  List<Session> get sessions => sessionService.sessions;

  // 分页数据 - 代理服务层数据
  PageData<Session> get pageData => sessionService.pageData;

  // 是否有更多数据
  bool get hasMoreData => sessionService.pageData.hasNext;

  // 当前分页请求
  PageRequest _currentRequest = PageRequest(
    page: 1,
    size: StringsConsts.defaultPageSize,
    // 移除自定义排序参数，使用后端默认排序
  );

  // 取消令牌标识符
  final String _cancelTokenId = 'sessions_controller';

  // 图片预加载器
  final ImagePreloader _imagePreloader = ImagePreloader();

  // 事件订阅
  StreamSubscription? _refreshDataSubscription;

  // UI层不再维护独立缓存，统一由服务层管理

  // 构造函数，支持依赖注入
  SessionsController({required this.sessionService});

  @override
  void onInit() {
    super.onInit();

    // 从服务器加载会话
    loadSessions();

    // 监听会话列表刷新事件
    _setupRefreshDataListener();

    // 预加载会话图片
    _preloadSessionImages();
  }

  /// 设置刷新数据监听器
  void _setupRefreshDataListener() {
    _refreshDataSubscription = EventBus().on('refresh_sessions_data').listen((event) {
      LogUtil.info('收到刷新会话列表事件: $event');

      // 获取事件数据
      final data = event['data'];
      if (data is Map) {
        final forceRefresh = data['forceRefresh'] as bool? ?? true;
        final source = data['source'] as String? ?? 'unknown';

        LogUtil.info('从[$source]触发刷新会话列表，强制刷新: $forceRefresh');

        // 执行刷新
        refreshSessions();
      } else {
        // 默认执行刷新
        refreshSessions();
      }
    });
  }

  /// 预加载会话图片（头像和封面）
  void _preloadSessionImages() {
    if (sessionService.sessions.isEmpty) return;

    final imagePreloader = ImagePreloader();

    // 收集所有有效的头像URL和封面URL
    final List<String> avatarUrls = [];
    final List<String> coverUrls = [];

    for (int i = 0; i < sessionService.sessions.length; i++) {
      final session = sessionService.sessions[i];

      // 获取头像URL
      if (session.avatarUrl != null && session.avatarUrl!.isNotEmpty &&
          !imagePreloader.isImagePreloaded(session.avatarUrl!)) {
        avatarUrls.add(session.avatarUrl!);
      }

      // 获取封面URL - 异步获取但不等待
      if (session.aiRoleId > 0) {
        _roleService.getCoverUrlById(session.aiRoleId).then((coverUrl) {
          if (coverUrl != null && coverUrl.isNotEmpty &&
              !imagePreloader.isImagePreloaded(coverUrl)) {
            // 预加载封面 - 高优先级
            imagePreloader.preloadImage(
              coverUrl,
              priority: ImagePreloadPriority.high,
              quality: ImagePreloadQuality.original,
            );
          }
        });
      }

      // 只收集前10个会话的图片
      if (i >= 10) break;
    }

    // 预加载头像 - 中优先级
    if (avatarUrls.isNotEmpty) {
      imagePreloader.preloadImages(
        avatarUrls,
        priority: ImagePreloadPriority.medium,
      );
    }
  }

  /// 加载会话列表
  Future<void> loadSessions({bool refresh = false}) async {
    if (isLoading.value && !refresh) {
      LogUtil.warn('正在加载会话列表，请等待当前操作完成...');
      return;
    }

    try {
      LogUtil.debug('加载会话列表，强制刷新: $refresh');

      // 重置错误消息
      errorMessage.value = '';

      // 如果是刷新或首次加载，重置到第一页
      if (refresh) {
        _currentRequest = _currentRequest.copyWith(
          page: 1,
          // 使用后端默认排序，不在前端指定排序规则
        );
      }

      // 使用接口定义的方法
      if (refresh) {
        await sessionService.refreshSessions(refresh: refresh);
      } else {
        await sessionService.loadSessions(
          page: _currentRequest.page,
          pageSize: _currentRequest.size,
        );
      }
    } catch (e) {
      LogUtil.error('加载会话列表失败: $e');
      errorMessage.value = '无法加载会话列表，请重试';
    }
  }

  /// 加载更多会话
  Future<void> loadMoreSessions() async {
    // 分页边界检查
    if (isLoadingMore.value) {
      LogUtil.debug('已有加载任务正在进行中，跳过重复请求');
      return;
    }

    if (!hasMoreData) {
      LogUtil.debug('没有更多数据了，跳过加载');
      return;
    }

    try {
      isLoadingMore.value = true;

      // 创建下一页请求
      _currentRequest = _currentRequest.nextPage();
      LogUtil.debug('加载更多会话：请求第${_currentRequest.page}页');

      // 委托给服务层处理分页加载和数据合并，使用后端默认排序
      await sessionService.loadMoreSessions(
        page: _currentRequest.page,
        pageSize: _currentRequest.size,
      );

    } catch (e) {
      LogUtil.error('加载更多会话失败: $e');
      // 回滚请求
      _currentRequest = _currentRequest.previousPage();
      // 统一使用ErrorHandler.handleException()方法
      ErrorHandler.handleException(e, showSnackbar: true);
    } finally {
      isLoadingMore.value = false;
    }
  }

  /// 打开会话
  ///
  /// [session] 要打开的会话
  void openSession(Session session) {
    // 获取AI角色的头像URL和封面URL
    final String avatarUrl = session.avatarUrl.isNotEmpty
        ? session.avatarUrl
        : ''; // 如果avatarUrl为空，则使用空字符串

    final String coverUrl = session.coverUrl.isNotEmpty
        ? session.coverUrl
        : ''; // 如果coverUrl为空，则使用空字符串

    LogUtil.info('打开会话: ID=${session.id}, 角色ID=${session.aiRoleId}, 头像URL=$avatarUrl, 封面URL=$coverUrl');

    // 确保角色ID有效
    if (session.aiRoleId <= 0) {
      LogUtil.warn('会话角色ID无效: ${session.aiRoleId}，使用默认角色ID代替');
    }

    // 预加载聊天页面所需图片
    _preloadChatImages(avatarUrl, coverUrl);

    // 使用RouterManager.navigateTo替代Get.toNamed，确保ChatBinding被正确执行
    RouterManager.navigateTo(Routes.chatScreen, arguments: {
      StringsConsts.userId: 'ai_${session.aiRoleId}',
      StringsConsts.username: session.title,
      StringsConsts.profilePic: avatarUrl,
      StringsConsts.isGroupChat: false,
      'conversationId': session.id,
      'aiRoleId': session.aiRoleId,
      'coverUrl': coverUrl,
      'avatarUrl': avatarUrl, // 额外添加avatarUrl字段，确保ChatController可以访问
      'description': session.description ?? '', // 添加角色描述
    });
  }

  /// 预加载聊天页面所需图片
  void _preloadChatImages(String avatarUrl, String coverUrl) {
    final imagePreloader = _imagePreloader;
    final List<String> chatImages = [];

    if (avatarUrl.isNotEmpty && !imagePreloader.isImagePreloaded(avatarUrl)) {
      chatImages.add(avatarUrl);
    }

    if (coverUrl.isNotEmpty && !imagePreloader.isImagePreloaded(coverUrl)) {
      chatImages.add(coverUrl);
    }

    if (chatImages.isNotEmpty) {
      // 高优先级预加载聊天页面图片
      imagePreloader.preloadImages(
        chatImages,
        width: 200,
        height: 200,
        priority: ImagePreloadPriority.high,
      );
    }
  }

  /// 删除会话
  Future<void> deleteSession(int id) async {
    if (isDeletingSession.value) {
      LogUtil.warn('会话删除操作正在进行中，请勿重复操作');
      return;
    }

    try {
      isDeletingSession.value = true;
      LogUtil.debug('开始删除会话，ID=$id');

      final success = await sessionService.deleteSession(id);

              if (success) {
          ToastUtil.success('Conversation deleted');

        // 刷新会话列表
        refreshSessions();

        // 如果当前页面会话数为0，且有前一页，则加载前一页
        if (sessions.isEmpty && _currentRequest.page > 1) {
          _currentRequest = _currentRequest.copyWith(
            page: _currentRequest.page - 1
          );
          loadSessions();
        }
              } else {
          ToastUtil.error('Failed to delete conversation');
      }
    } catch (e) {
      LogUtil.error('删除会话失败: $e');
      ErrorHandler.handleException(e);
    } finally {
      isDeletingSession.value = false;
    }
  }

  /// 切换会话置顶状态（乐观更新 - 最佳实践版本）
  Future<void> togglePinSession(int id) async {
    try {
      LogUtil.debug('开始切换会话置顶状态，ID=$id');

      // 查找当前会话
      final sessionIndex = sessions.indexWhere((session) => session.id == id);
      if (sessionIndex == -1) {
        LogUtil.warn('要切换置顶状态的会话不存在，ID=$id');
        return;
      }

      final originalSession = sessions[sessionIndex];
      final newPinnedState = !originalSession.isPinned;

      // 第一步：乐观更新 - 立即更新置顶状态，给用户即时反馈
      final optimisticSession = originalSession.copyWith(isPinned: newPinnedState);
      sessions[sessionIndex] = optimisticSession;

      LogUtil.debug('乐观更新完成，新状态: ${newPinnedState ? "置顶" : "取消置顶"}');

      // 第二步：发送网络请求
      bool success = false;
      try {
        if (newPinnedState) {
          success = await sessionService.pinSession(id);
        } else {
          success = await sessionService.unpinSession(id);
        }

        if (success) {
          LogUtil.debug('置顶状态更新成功');
          // 第三步：静默刷新以获取正确的排序，不影响用户体验
          _silentRefreshSessions();
        } else {
          // 失败时回滚乐观更新
          LogUtil.warn('置顶状态更新失败，回滚UI更新');
          sessions[sessionIndex] = originalSession;
          ToastUtil.error('Failed to update conversation');
        }
      } catch (e) {
        // 网络请求异常时回滚
        LogUtil.error('置顶状态网络请求失败: $e');
        sessions[sessionIndex] = originalSession;
        ToastUtil.error('Failed to update conversation');
      }
    } catch (e) {
      LogUtil.error('切换会话置顶状态失败: $e');
      // 异常时刷新列表以确保数据一致性
      refreshSessions();
      ErrorHandler.handleException(e);
    }
  }

  /// 静默刷新会话列表（不显示加载状态）
  /// 用于在后台同步数据而不影响用户体验
  Future<void> _silentRefreshSessions() async {
    try {
      LogUtil.debug('开始静默刷新会话列表');

      // 使用 refreshSessions 方法静默刷新，不显示加载状态
      await sessionService.refreshSessions(refresh: true);

      LogUtil.debug('静默刷新完成，会话数量: ${sessions.length}');
    } catch (e) {
      LogUtil.error('静默刷新会话列表失败: $e');
      // 静默刷新失败不影响用户体验，只记录日志
    }
  }

  /// 隐藏会话（乐观更新 - 改进版本）
  Future<void> hideSession(int id) async {
    try {
      LogUtil.debug('开始隐藏会话，ID=$id');

      // 查找当前会话
      final sessionIndex = sessions.indexWhere((session) => session.id == id);
      if (sessionIndex == -1) {
        LogUtil.warn('要隐藏的会话不存在，ID=$id');
        return;
      }

      final originalSession = sessions[sessionIndex];
      final originalIndex = sessionIndex;

      // 乐观更新：先从UI中移除会话
      sessions.removeAt(sessionIndex);
      LogUtil.debug('乐观更新完成，会话已从列表中移除');

      // 发送网络请求
      bool success = await sessionService.hideSession(id);

      if (success) {
        LogUtil.debug('会话隐藏成功');
        ToastUtil.success('Conversation hidden');
        // 成功后不需要额外操作，会话已经从列表中移除
        // 可以选择性地刷新列表以确保数据一致性
        // refreshSessions();
      } else {
        // 失败时回滚乐观更新：重新添加会话到原位置
        LogUtil.warn('会话隐藏失败，回滚UI更新');
        // 确保插入位置不超出列表范围
        final insertIndex = originalIndex > sessions.length ? sessions.length : originalIndex;
        sessions.insert(insertIndex, originalSession);
        ToastUtil.error('Failed to hide conversation');
      }
    } catch (e) {
      LogUtil.error('隐藏会话失败: $e');
      // 异常时刷新列表以确保数据一致性
      refreshSessions();
      ErrorHandler.handleException(e);
    }
  }

  /// 刷新会话
  ///
  /// 用户下拉刷新时调用
  Future<void> refreshSessions() async {
    await loadSessions(refresh: true);
  }

  /// 处理滚动通知，用于自动加载更多
  bool handleScrollNotification(ScrollNotification notification) {
    if (notification is ScrollEndNotification &&
        notification.metrics.extentAfter < StringsConsts.loadMoreThreshold &&
        hasMoreData) {
      loadMoreSessions();
    }
    return false;
  }

  /// 添加过滤条件
  void addFilter(String key, dynamic value) {
    _currentRequest = _currentRequest.addFilter(key, value);
    loadSessions(refresh: true);
  }

  /// 移除过滤条件
  void removeFilter(String key) {
    _currentRequest = _currentRequest.removeFilter(key);
    loadSessions(refresh: true);
  }

  // 已移除前端排序功能，完全依赖后端排序

  @override
  void onClose() {
    // 取消事件订阅
    if (_refreshDataSubscription != null) {
      _refreshDataSubscription?.cancel();
      LogUtil.debug('SessionsController: 已取消数据刷新事件订阅');
      _refreshDataSubscription = null;
    }

    // 取消所有网络请求
    HttpManager.cancelRequests(_cancelTokenId);
    LogUtil.debug('SessionsController 已关闭，取消了所有网络请求');

    super.onClose();
  }
}