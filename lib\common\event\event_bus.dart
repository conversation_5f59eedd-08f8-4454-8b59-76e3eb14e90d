import 'package:get/get.dart';
import 'package:rxdart/rxdart.dart';
import 'package:rolio/common/utils/logger.dart';

/// 定义应用内事件类型
class AppEvent {
  // 消息相关事件
  static const String messageAdded = 'message_added';
  static const String messageUpdated = 'message_updated';
  static const String messageDeleted = 'message_deleted';
  
  // 会话相关事件
  static const String sessionUpdated = 'session_updated';
  static const String sessionCreated = 'session_created';
  static const String sessionDeleted = 'session_deleted';
  
  // AI相关事件
  static const String aiReplyStarted = 'ai_reply_started';
  static const String aiReplyFinished = 'ai_reply_finished';
  static const String nonActiveRoleMessage = 'non_active_role_message';
  static const String roleMessage = 'role_message'; // 新增：统一的角色消息事件
  static const String activeRoleChanged = 'active_role_changed'; // 新增：活跃角色变更事件
  
  // 角色会话绑定相关事件
  static const String roleConversationBound = 'role_conversation_bound';
  static const String roleConversationUpdated = 'role_conversation_updated';
  static const String roleConversationBindingUpdate = 'role_conversation_binding_update';
  
  // 角色收藏相关事件
  static const String roleFavoriteStatusChanged = 'role_favorited_status_changed';
  static const String roleFavoriteListUpdated = 'role_favorite_list_updated';
  
  // 用户身份相关事件
  static const String userIdentityChanged = 'user_identity_changed';
  
  // WebSocket相关事件
  static const String websocketDisconnected = 'websocket_disconnected';
  static const String aiReplyReset = 'ai_reply_reset';
  
  // 认证流程控制事件
  static const String authProcessStarted = 'auth_process_started';
  static const String authProcessCompleted = 'auth_process_completed';
  static const String tokenValidated = 'token_validated';
  static const String websocketConnectionAllowed = 'websocket_connection_allowed';
  
  // 资源清理相关事件
  static const String roleResourceCleanup = 'role_resource_cleanup'; // 清理特定角色的资源
  static const String conversationResourceCleanup = 'conversation_resource_cleanup'; // 清理特定会话的资源
  static const String sessionCacheCleanup = 'session_cache_cleanup'; // 清理会话缓存
  static const String roleBindingCleanup = 'role_binding_cleanup'; // 清理角色绑定关系
}

/// 事件总线服务
/// 
/// 用于在不同模块间传递事件，减少直接依赖，降低耦合度
class EventBus extends GetxService {
  // 单例模式
  static final EventBus _instance = EventBus._internal();
  factory EventBus() => _instance;
  EventBus._internal();
  
  // 事件流
  final _eventStream = PublishSubject<Map<String, dynamic>>();
  
  // 事件去重缓存 - 用于防止短时间内重复触发相同事件
  final Map<String, int> _recentEvents = {};
  
  // 需要去重的事件类型
  final Set<String> _deduplicateEvents = {
    AppEvent.websocketDisconnected,
    AppEvent.aiReplyReset,
  };
  
  // 去重时间窗口（毫秒）
  static const int _deduplicationWindowMs = 300;
  
  /// 发布事件
  /// 
  /// [eventName] 事件名称，建议使用AppEvent中定义的常量
  /// [data] 事件数据，可以是任何类型
  void fire(String eventName, dynamic data) {
    final now = DateTime.now().millisecondsSinceEpoch;
    
    // 检查是否需要去重的事件
    if (_deduplicateEvents.contains(eventName)) {
      // 生成事件标识符
      final String eventKey = _generateEventKey(eventName, data);
      
      // 检查是否在去重窗口期内
      if (_recentEvents.containsKey(eventKey)) {
        final lastTime = _recentEvents[eventKey]!;
        if (now - lastTime < _deduplicationWindowMs) {
          // 在去重窗口期内，跳过此事件
          LogUtil.debug('跳过重复事件: $eventName, 间隔: ${now - lastTime}ms');
          return;
        }
      }
      
      // 更新事件时间戳
      _recentEvents[eventKey] = now;
      
      // 清理过期事件记录
      _cleanupRecentEvents(now);
    }
    
    // 添加时间戳到事件数据
    final eventData = (data is Map) ? Map<String, dynamic>.from(data) : <String, dynamic>{};
    if (data is Map) {
      eventData.addAll({'timestamp': now});
    } else {
      eventData['data'] = data;
      eventData['timestamp'] = now;
    }
    
    // 发布事件
    _eventStream.add({
      'eventName': eventName,
      'data': eventData,
      'timestamp': now
    });
  }
  
  /// 生成事件标识符
  String _generateEventKey(String eventName, dynamic data) {
    if (data is Map && data.containsKey('id')) {
      return '$eventName:${data['id']}';
    }
    return eventName;
  }
  
  /// 清理过期的事件记录
  void _cleanupRecentEvents(int currentTime) {
    if (_recentEvents.length > 100) {
      final keysToRemove = <String>[];
      
      _recentEvents.forEach((key, timestamp) {
        if (currentTime - timestamp > 5000) { // 5秒过期
          keysToRemove.add(key);
        }
      });
      
      for (final key in keysToRemove) {
        _recentEvents.remove(key);
      }
      
      if (keysToRemove.isNotEmpty) {
        LogUtil.debug('已清理${keysToRemove.length}个过期事件记录');
      }
    }
  }
  
  /// 监听特定事件
  /// 
  /// [eventName] 事件名称，建议使用AppEvent中定义的常量
  /// 返回事件流
  Stream<Map<String, dynamic>> on(String eventName) {
    return _eventStream.stream.where((event) => event['eventName'] == eventName);
  }
  
  /// 监听所有事件
  /// 
  /// 返回所有事件流
  Stream<Map<String, dynamic>> get onAll => _eventStream.stream;
  
  /// 关闭事件总线
  void close() {
    _eventStream.close();
    _recentEvents.clear();
  }
} 