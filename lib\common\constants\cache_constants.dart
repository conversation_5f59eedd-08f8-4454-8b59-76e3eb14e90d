import 'package:flutter/foundation.dart';
import 'package:get/get.dart';
import 'package:rolio/common/cache/cache_manager.dart';

/// 缓存相关常量
class CacheConstants {
  /// 默认图片缓存大小 (100MB)
  static const int defaultImageCacheSizeBytes = 100 * 1024 * 1024;
  
  /// 低内存设备图片缓存大小 (50MB)
  static const int lowMemoryImageCacheSizeBytes = 50 * 1024 * 1024;
  
  /// 图片缓存过期时间 (7天)
  static const Duration imageCacheDuration = Duration(days: 7);
  
  /// 内存缓存默认过期时间 (1小时)
  static const Duration memoryDefaultExpiry = Duration(hours: 1);
  
  /// 本地持久化缓存默认过期时间 (7天)
  static const Duration persistentDefaultExpiry = Duration(days: 7);
  
  /// 内存监控检查间隔 (30秒)
  static const Duration memoryMonitorInterval = Duration(seconds: 30);
  
  /// 内存压力中等阈值 (70%)
  static const double moderateMemoryPressureThreshold = 70.0;
  
  /// 内存压力严重阈值 (85%)
  static const double severeMemoryPressureThreshold = 85.0;
  
  /// 调试模式下是否启用详细日志
  static final bool enableVerboseLogging = kDebugMode;
  
  /// 获取当前设备适合的图片缓存大小
  static int getRecommendedImageCacheSize() {
    // TODO: 根据设备信息动态计算缓存大小
    // 目前先使用固定值
    return defaultImageCacheSizeBytes;
  }
  
  /// 获取内存缓存默认过期时间(毫秒)
  static int getMemoryExpiryMs() {
    return memoryDefaultExpiry.inMilliseconds;
  }
  
  /// 获取持久化缓存默认过期时间(毫秒)
  static int getPersistentExpiryMs() {
    return persistentDefaultExpiry.inMilliseconds;
  }
  
  // ----------------------
  // 统一缓存配置 (新增)
  // ----------------------
  
  /// 默认缓存策略
  static const CacheStrategy defaultCacheStrategy = CacheStrategy.both;
  static const CacheStrategy defaultGetStrategy = CacheStrategy.memoryThenPersistent;
  
  /// 默认缓存过期时间（毫秒）
  static const int shortExpiryMs = 3 * 60 * 1000;  // 3分钟
  static const int mediumExpiryMs = 10 * 60 * 1000; // 10分钟
  static const int longExpiryMs = 30 * 60 * 1000;   // 30分钟
  
  /// 会话模块
  static const int sessionListExpiryMs = mediumExpiryMs;
  static const int sessionDetailExpiryMs = mediumExpiryMs;
  
  /// 角色模块 - 统一缓存时间配置

  static const int roleListExpiryMs = mediumExpiryMs;      // 10分钟 - 统一角色列表缓存时间
  static const int roleDetailExpiryMs = mediumExpiryMs;    // 10分钟 - 统一角色详情缓存时间
  static const int roleFavoriteExpiryMs = shortExpiryMs;   // 3分钟 - 收藏状态变化频繁
  static const int roleSearchExpiryMs = shortExpiryMs;     // 3分钟 - 搜索结果缓存时间
  
  /// 聊天模块
  static const int chatHistoryExpiryMs = longExpiryMs;     // 30分钟
  static const int chatPendingExpiryMs = 20 * 1000;        // 20秒
  
  /// 图片缓存
  static const int avatarCacheExpiryMs = 12 * 60 * 60 * 1000; // 12小时
  
  /// 获取指定模块的缓存过期时间
  static int getExpiryForModule(String module, {String? type}) {
    switch (module) {
      case 'session':
        return type == 'detail' ? sessionDetailExpiryMs : sessionListExpiryMs;
      case 'role':
        if (type == 'detail') return roleDetailExpiryMs;
        if (type == 'favorite') return roleFavoriteExpiryMs;
        if (type == 'search') return roleSearchExpiryMs;
        return roleListExpiryMs;
      case 'chat':
        return type == 'pending' ? chatPendingExpiryMs : chatHistoryExpiryMs;
      case 'image':
        return type == 'avatar' ? avatarCacheExpiryMs : imageCacheDuration.inMilliseconds;
      default:
        return mediumExpiryMs;
    }
  }

  /// 统一缓存键前缀配置
  static const String recommendRoleListCacheKey = 'recommend_role_list';
  static const String roleListCachePrefix = 'role_list_';
  static const String roleDetailCachePrefix = 'role_detail_';
  static const String roleSearchCachePrefix = 'role_search_';
  static const String recommendCachePrefix = 'recommend_roles_';
  static const String sessionListCachePrefix = 'session_list_';
  static const String sessionDetailCachePrefix = 'session_detail_';
  
  /// 获取指定模块的缓存策略
  static CacheStrategy getStrategyForModule(String module, {bool isRead = true}) {
    if (isRead) {
      return defaultGetStrategy;
    }
    return defaultCacheStrategy;
  }
} 