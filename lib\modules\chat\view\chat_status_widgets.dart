import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:rolio/common/utils/toast_util.dart';
import 'package:rolio/widgets/ai_avatar.dart';
import 'package:rolio/widgets/cover_image.dart';
import 'package:rolio/common/utils/logger.dart';
import 'package:rolio/common/constants/ws_constants.dart';
import 'package:rolio/manager/ws_manager.dart';

/// 聊天加载视图
/// 
/// 在聊天页面加载时显示，包含转圈特效和背景封面图
class ChatLoadingView extends StatefulWidget {
  final String? avatarUrl;
  final String? coverUrl;
  final String? roleName;
  final int aiRoleId;

  const ChatLoadingView({
    Key? key,
    this.avatarUrl,
    this.coverUrl,
    this.roleName,
    this.aiRoleId = 0,
  }) : super(key: key);

  @override
  State<ChatLoadingView> createState() => _ChatLoadingViewState();
}

class _ChatLoadingViewState extends State<ChatLoadingView> with SingleTickerProviderStateMixin {
  late AnimationController _animationController;

  @override
  void initState() {
    super.initState();
    // 创建动画控制器，用于控制转圈特效
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    )..repeat();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    LogUtil.debug('显示聊天加载视图，直接展示封面并添加转圈特效');
    
    return Stack(
      children: [
        // 背景封面图层，直接展示
        if (widget.coverUrl != null && widget.coverUrl!.isNotEmpty)
          Positioned.fill(
            child: ChatCoverImage(
              imageUrl: widget.coverUrl!,
              width: MediaQuery.of(context).size.width,
              height: MediaQuery.of(context).size.height,
            ),
          ),
        
        // 转圈特效
        Positioned.fill(
          child: Center(
            child: _buildLoadingSpinner(),
          ),
        ),
      ],
    );
  }
  
  // 构建转圈特效
  Widget _buildLoadingSpinner() {
    return Container(
      width: 120,
      height: 120,
      decoration: BoxDecoration(
        color: Colors.transparent,
        borderRadius: BorderRadius.circular(20),
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          // 转圈动画
          SizedBox(
            width: 50,
            height: 50,
            child: CircularProgressIndicator(
              valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
              strokeWidth: 3,
            ),
          ),
          SizedBox(height: 16),
          // 加载文本
          Text(
            'Loading...',
            style: TextStyle(
              color: Colors.white,
              fontSize: 16,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }
}

/// WebSocket重连提示组件
///
/// 当WebSocket连接断开并达到最大重连次数时，显示提示让用户手动重连
class WsReconnectWidget extends StatelessWidget {
  final WsManager _wsManager = Get.find<WsManager>();
  
  WsReconnectWidget({Key? key}) : super(key: key);
  
  @override
  Widget build(BuildContext context) {
    return StreamBuilder<ReconnectStatus>(
      stream: _wsManager.reconnectStream,
      builder: (context, snapshot) {
        // 如果没有数据或不是最大重连次数，不显示任何内容
        if (!snapshot.hasData || !snapshot.data!.isMaxAttempts) {
          return const SizedBox.shrink();
        }
        
        // 达到最大重连次数，显示重连提示
        return Container(
          padding: const EdgeInsets.symmetric(vertical: 8.0, horizontal: 16.0),
          margin: const EdgeInsets.all(8.0),
          decoration: BoxDecoration(
            color: Colors.red.shade50,
            borderRadius: BorderRadius.circular(8.0),
            border: Border.all(color: Colors.red.shade200),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Row(
                children: [
                  Icon(Icons.error_outline, color: Colors.red.shade700),
                  const SizedBox(width: 8.0),
                  Expanded(
                    child: Text(
                      'Connection lost, auto-reconnect failed',
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        color: Colors.red.shade700,
                      ),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 8.0),
              Text(
                'Please check your network and click the button below to reconnect',
                style: TextStyle(
                  fontSize: 12.0,
                  color: Colors.grey.shade700,
                ),
              ),
              const SizedBox(height: 12.0),
              ElevatedButton(
                onPressed: _handleManualReconnect,
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.red.shade100,
                  foregroundColor: Colors.red.shade700,
                ),
                child: const Text('Reconnect'),
              ),
            ],
          ),
        );
      },
    );
  }
  
  /// 处理手动重连
  void _handleManualReconnect() async {
    try {
      final result = await _wsManager.reconnect();
      if (result) {
        LogUtil.info('手动重连成功');
        ToastUtil.success('Successfully reconnected to server');
      } else {
        LogUtil.warn('手动重连失败');
        ToastUtil.error('Unable to connect to server, please check your network and try again');
      }
    } catch (e) {
      LogUtil.error('手动重连异常: $e');
      ToastUtil.error('Error during reconnection, please try again later');
    }
  }
} 