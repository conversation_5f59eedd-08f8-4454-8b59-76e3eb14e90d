import 'dart:convert';
import 'dart:io';
import 'package:path_provider/path_provider.dart';
import 'package:rolio/common/utils/logger.dart';
import 'package:shared_preferences/shared_preferences.dart';

/// 持久化缓存管理类
/// 
/// 提供文件系统和SharedPreferences的持久化缓存实现
class PersistentCache {
  /// SharedPreferences实例
  SharedPreferences? _prefs;
  
  /// 缓存目录
  Directory? _cacheDir;
  
  /// 缓存元数据键前缀
  static const String _metaKeyPrefix = 'cache_meta_';
  
  /// 缓存数据键前缀
  static const String _dataKeyPrefix = 'cache_data_';
  
  /// 默认过期时间（毫秒），默认24小时
  int _defaultExpiry = 24 * 60 * 60 * 1000; // 24小时
  
  /// 构造函数
  PersistentCache({int? defaultExpiry}) {
    if (defaultExpiry != null && defaultExpiry > 0) {
      _defaultExpiry = defaultExpiry;
    }
  }
  
  /// 初始化
  Future<void> init() async {
    if (_prefs != null) {
      return;
    }
    
    try {
      _prefs = await SharedPreferences.getInstance();
      
      // 获取应用缓存目录
      _cacheDir = await getApplicationCacheDirectory();
      
      // 创建缓存目录（如果不存在）
      final cachePath = '${_cacheDir!.path}/app_cache';
      final cacheDir = Directory(cachePath);
      if (!await cacheDir.exists()) {
        await cacheDir.create(recursive: true);
      }
      
      _cacheDir = cacheDir;
      
      // 清理过期缓存
      _cleanupExpiredItems();
      
      LogUtil.debug('持久化缓存初始化完成，缓存目录: ${_cacheDir?.path}');
    } catch (e) {
      LogUtil.error('持久化缓存初始化失败: $e');
    }
  }
  
  /// 设置默认过期时间
  void setDefaultExpiry(int milliseconds) {
    if (milliseconds > 0) {
      _defaultExpiry = milliseconds;
    }
  }
  
  /// 获取缓存
  Future<T?> get<T>(
    String key, {
    int? maxAge,
    T? Function(Map<String, dynamic>)? fromJson,
  }) async {
    await init();
    
    try {
      // 获取缓存元数据
      final metaKey = _getMetaKey(key);
      final metaString = _prefs?.getString(metaKey);
      
      if (metaString == null) {
        return null;
      }
      
      // 解析元数据
      final meta = jsonDecode(metaString) as Map<String, dynamic>;
      final expiryTime = meta['expiry'] as int?;
      final createTime = meta['create_time'] as int?;
      
      if (expiryTime == null) {
        return null;
      }
      
      // 检查是否过期
      final now = DateTime.now().millisecondsSinceEpoch;
      if (now >= expiryTime) {
        await remove(key);
        return null;
      }
      
      // 检查最大有效期
      if (maxAge != null && createTime != null) {
        final age = now - createTime;
        if (age > maxAge) {
          await remove(key);
          return null;
        }
      }
      
      // 获取缓存数据
      final dataKey = _getDataKey(key);
      final valueString = _prefs?.getString(dataKey);
      
      if (valueString == null) {
        return null;
      }
      
      // 如果是文件引用，则从文件中读取
      if (valueString.startsWith('file:')) {
        final filePath = valueString.substring(5);
        final file = File(filePath);
        
        if (!await file.exists()) {
          await remove(key);
          return null;
        }
        
        final fileContent = await file.readAsString();
        
        if (T == String) {
          return fileContent as T;
        }
        
        try {
          if (fromJson != null) {
            final map = jsonDecode(fileContent) as Map<String, dynamic>;
            return fromJson(map);
          } else if (T == Map) {
            return jsonDecode(fileContent) as T;
          } else {
            return fileContent as T;
          }
        } catch (e) {
          LogUtil.error('持久化缓存文件解析失败: $key, 错误: $e');
          return null;
        }
      }
      
      // 普通值处理
      if (T == String) {
        return valueString as T;
      }
      
      try {
        if (fromJson != null) {
          final map = jsonDecode(valueString) as Map<String, dynamic>;
          return fromJson(map);
        } else if (T == Map || T == Map<String, dynamic>) {
          return jsonDecode(valueString) as T;
        } else {
          // 尝试将字符串转换为请求的类型
          final decoded = jsonDecode(valueString);
          return decoded as T;
        }
      } catch (e) {
        LogUtil.error('持久化缓存JSON解析失败: $key, 错误: $e');
        return null;
      }
    } catch (e) {
      LogUtil.error('持久化缓存获取失败: $key, 错误: $e');
      return null;
    }
  }
  
  /// 设置缓存
  Future<bool> set<T>(
    String key, 
    T data, {
    int? expiry,
    Map<String, dynamic> Function(T)? toJson,
  }) async {
    await init();
    
    if (_prefs == null) {
      return false;
    }
    
    try {
      final now = DateTime.now().millisecondsSinceEpoch;
      final expiryTime = now + (expiry ?? _defaultExpiry);
      
      // 创建元数据
      final meta = {
        'key': key,
        'create_time': now,
        'expiry': expiryTime,
        'type': T.toString(),
      };
      
      final metaKey = _getMetaKey(key);
      final dataKey = _getDataKey(key);
      
      // 将元数据保存到SharedPreferences
      await _prefs!.setString(metaKey, jsonEncode(meta));
      
      // 保存数据
      if (data is String) {
        // 如果数据较大，保存到文件中
        if (data.length > 10 * 1024) { // 超过10KB保存到文件
          final filePath = '${_cacheDir!.path}/${_generateFileName(key)}';
          final file = File(filePath);
          await file.writeAsString(data);
          await _prefs!.setString(dataKey, 'file:$filePath');
        } else {
          // 否则直接保存到SharedPreferences
          await _prefs!.setString(dataKey, data);
        }
      } else {
        // 将非字符串数据转换为JSON字符串
        String jsonString;
        if (toJson != null) {
          // 使用自定义序列化
          final jsonMap = toJson(data);
          jsonString = jsonEncode(jsonMap);
        } else {
          // 尝试默认序列化
          jsonString = jsonEncode(data);
        }
        
        // 如果数据较大，保存到文件中
        if (jsonString.length > 10 * 1024) {
          final filePath = '${_cacheDir!.path}/${_generateFileName(key)}';
          final file = File(filePath);
          await file.writeAsString(jsonString);
          await _prefs!.setString(dataKey, 'file:$filePath');
        } else {
          await _prefs!.setString(dataKey, jsonString);
        }
      }
      
      return true;
    } catch (e) {
      LogUtil.error('持久化缓存设置失败: $key, 错误: $e');
      return false;
    }
  }
  
  /// 删除缓存
  Future<bool> remove(String key) async {
    await init();
    
    if (_prefs == null) {
      return false;
    }
    
    try {
      final metaKey = _getMetaKey(key);
      final dataKey = _getDataKey(key);
      
      // 检查数据是否为文件引用
      final valueString = _prefs!.getString(dataKey);
      if (valueString != null && valueString.startsWith('file:')) {
        try {
          final filePath = valueString.substring(5);
          final file = File(filePath);
          if (await file.exists()) {
            await file.delete();
          }
        } catch (e) {
          LogUtil.warn('删除缓存文件失败: $key, 错误: $e');
        }
      }
      
      // 删除SharedPreferences中的元数据和数据
      await _prefs!.remove(metaKey);
      await _prefs!.remove(dataKey);
      
      return true;
    } catch (e) {
      LogUtil.error('持久化缓存删除失败: $key, 错误: $e');
      return false;
    }
  }
  
  /// 清空所有缓存
  Future<bool> clear() async {
    await init();
    
    if (_prefs == null || _cacheDir == null) {
      return false;
    }
    
    try {
      // 获取所有缓存键
      final allKeys = _prefs!.getKeys();
      final metaKeys = allKeys.where((k) => k.startsWith(_metaKeyPrefix)).toList();
      
      // 删除所有缓存文件
      for (final metaKey in metaKeys) {
        final key = metaKey.substring(_metaKeyPrefix.length);
        await remove(key);
      }
      
      // 清空缓存目录中的所有文件
      final dir = Directory(_cacheDir!.path);
      if (await dir.exists()) {
        await for (final entity in dir.list()) {
          if (entity is File) {
            await entity.delete();
          }
        }
      }
      
      LogUtil.debug('持久化缓存已清空');
      return true;
    } catch (e) {
      LogUtil.error('清空持久化缓存失败: $e');
      return false;
    }
  }
  
  /// 检查缓存是否存在
  Future<bool> exists(String key) async {
    await init();
    
    if (_prefs == null) {
      return false;
    }
    
    try {
      final metaKey = _getMetaKey(key);
      final metaString = _prefs!.getString(metaKey);
      
      if (metaString == null) {
        return false;
      }
      
      // 检查是否过期
      final meta = jsonDecode(metaString) as Map<String, dynamic>;
      final expiryTime = meta['expiry'] as int?;
      
      if (expiryTime == null) {
        return false;
      }
      
      final now = DateTime.now().millisecondsSinceEpoch;
      if (now >= expiryTime) {
        await remove(key); // 自动删除过期缓存
        return false;
      }
      
      // 检查数据是否存在
      final dataKey = _getDataKey(key);
      final valueString = _prefs!.getString(dataKey);
      
      if (valueString == null) {
        return false;
      }
      
      // 如果是文件引用，检查文件是否存在
      if (valueString.startsWith('file:')) {
        final filePath = valueString.substring(5);
        final file = File(filePath);
        return await file.exists();
      }
      
      return true;
    } catch (e) {
      LogUtil.error('检查持久化缓存是否存在失败: $key, 错误: $e');
      return false;
    }
  }
  
  /// 获取缓存过期时间
  Future<int?> getExpiryTime(String key) async {
    await init();
    
    if (_prefs == null) {
      return null;
    }
    
    try {
      final metaKey = _getMetaKey(key);
      final metaString = _prefs!.getString(metaKey);
      
      if (metaString == null) {
        return null;
      }
      
      // 解析元数据
      final meta = jsonDecode(metaString) as Map<String, dynamic>;
      final expiryTime = meta['expiry'] as int?;
      
      if (expiryTime == null) {
        return null;
      }
      
      // 检查是否已经过期
      final now = DateTime.now().millisecondsSinceEpoch;
      if (now >= expiryTime) {
        await remove(key); // 自动删除过期缓存
        return null;
      }
      
      return expiryTime;
    } catch (e) {
      LogUtil.error('获取持久化缓存过期时间失败: $key, 错误: $e');
      return null;
    }
  }
  
  /// 获取所有缓存键
  Future<List<String>> getKeys() async {
    await init();
    
    if (_prefs == null) {
      return [];
    }
    
    try {
      // 获取所有缓存元数据键
      final allKeys = _prefs!.getKeys();
      final metaKeys = allKeys.where((k) => k.startsWith(_metaKeyPrefix)).toList();
      
      // 提取原始键（去除前缀）
      final keys = metaKeys.map((metaKey) => 
        metaKey.substring(_metaKeyPrefix.length)
      ).toList();
      
      return keys;
    } catch (e) {
      LogUtil.error('获取持久化缓存键失败: $e');
      return [];
    }
  }
  
  /// 获取元数据键
  String _getMetaKey(String key) {
    return '$_metaKeyPrefix$key';
  }
  
  /// 获取数据键
  String _getDataKey(String key) {
    return '$_dataKeyPrefix$key';
  }
  
  /// 生成文件名
  String _generateFileName(String key) {
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    final hash = key.hashCode.abs();
    return 'cache_${hash}_$timestamp.dat';
  }
  
  /// 清理过期缓存
  Future<void> _cleanupExpiredItems() async {
    if (_prefs == null) {
      return;
    }
    
    try {
      // 获取当前时间
      final now = DateTime.now().millisecondsSinceEpoch;
      
      // 获取所有缓存元数据键
      final allKeys = _prefs!.getKeys();
      final metaKeys = allKeys.where((k) => k.startsWith(_metaKeyPrefix)).toList();
      
      int removedCount = 0;
      
      for (final metaKey in metaKeys) {
        final metaString = _prefs!.getString(metaKey);
        if (metaString == null) {
          continue;
        }
        
        try {
          final meta = jsonDecode(metaString) as Map<String, dynamic>;
          final expiryTime = meta['expiry'] as int?;
          final key = metaKey.substring(_metaKeyPrefix.length);
          
          if (expiryTime != null && now >= expiryTime) {
            await remove(key);
            removedCount++;
          }
        } catch (e) {
          // 忽略解析错误
          continue;
        }
      }
      
      if (removedCount > 0) {
        LogUtil.debug('持久化缓存清理完成，移除了 $removedCount 个过期项');
      }
    } catch (e) {
      LogUtil.error('清理持久化缓存失败: $e');
    }
  }
} 