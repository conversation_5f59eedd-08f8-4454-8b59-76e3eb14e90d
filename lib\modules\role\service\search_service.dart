import 'dart:async';

import 'package:get/get.dart';
import 'package:rolio/common/models/ai_role.dart';
import 'package:rolio/common/utils/logger.dart';
import 'package:rolio/modules/role/repository/search_repository.dart';
import 'package:rolio/common/utils/error_handler.dart';

/// 搜索服务
/// 处理角色搜索相关的业务逻辑
class SearchService {
  // 搜索仓库
  final SearchRepository _repository = Get.find<SearchRepository>();

  // 搜索防抖计时器
  Timer? _debounceTimer;

  // 当前搜索请求的取消令牌
  Completer<void>? _currentSearchCancellation;

  // 防抖时间(毫秒) - 增加到500ms以减少频繁请求
  static const int _debounceTime = 500;
  
  /// 搜索角色
  ///
  /// 根据关键词和过滤条件搜索角色
  /// [keyword] 搜索关键词
  /// [page] 页码
  /// [pageSize] 每页大小
  /// [forceRefresh] 是否强制刷新
  /// 返回搜索结果及分页信息
  Future<Map<String, dynamic>> searchRoles({
    required String keyword,
    int page = 1,
    int pageSize = 20,
    bool forceRefresh = false,
  }) async {
    // 取消之前的搜索请求
    _cancelCurrentSearch();

    // 创建新的取消令牌
    _currentSearchCancellation = Completer<void>();
    final cancellationToken = _currentSearchCancellation!;

    try {
      LogUtil.debug('搜索角色服务: 关键词=$keyword, 页码=$page');

      // 检查是否已被取消
      if (cancellationToken.isCompleted) {
        LogUtil.debug('搜索请求已被取消');
        return _getEmptyResult(page, pageSize);
      }

      // 调用仓库搜索角色
      final result = await _repository.searchRoles(
        keyword: keyword,
        page: page,
        size: pageSize,
        forceRefresh: forceRefresh,
      );

      // 再次检查是否已被取消
      if (cancellationToken.isCompleted) {
        LogUtil.debug('搜索请求在完成前被取消');
        return _getEmptyResult(page, pageSize);
      }

      return result;
    } catch (e) {
      // 检查是否是因为取消导致的错误
      if (cancellationToken.isCompleted) {
        LogUtil.debug('搜索请求被取消: $e');
        return _getEmptyResult(page, pageSize);
      }

      LogUtil.error('搜索角色服务错误: $e');
      
      // 使用ErrorHandler处理异常
      ErrorHandler.handleException(
        e,
        message: '搜索角色失败，请检查网络连接并稍后再试',
        showSnackbar: true,
      );
      
      return _getEmptyResult(page, pageSize);
    }
  }

  /// 获取空的搜索结果
  Map<String, dynamic> _getEmptyResult(int page, int pageSize) {
    return {
      'items': <AiRole>[],
      'total': 0,
      'page': page,
      'size': pageSize,
      'pages': 0
    };
  }

  /// 取消当前搜索请求
  void _cancelCurrentSearch() {
    if (_currentSearchCancellation != null && !_currentSearchCancellation!.isCompleted) {
      _currentSearchCancellation!.complete();
      LogUtil.debug('已取消当前搜索请求');
    }
  }
  
  /// 获取搜索建议(带防抖)
  /// 
  /// 根据输入的关键词返回匹配的角色名称建议列表
  /// [keyword] 搜索关键词
  /// [limit] 匹配数量限制
  /// [callback] 回调函数，用于接收搜索建议
  void getSearchSuggestions(
    String keyword, {
    int limit = 10, 
    required Function(List<String>) callback
  }) {
    // 取消上一次的计时器
    _debounceTimer?.cancel();
    
    // 如果关键词为空，直接返回空列表
    if (keyword.isEmpty) {
      callback([]);
      return;
    }
    
    // 创建新的防抖计时器
    _debounceTimer = Timer(const Duration(milliseconds: _debounceTime), () async {
      try {
        final suggestions = await _repository.getSearchSuggestions(keyword, limit: limit);
        callback(suggestions);
      } catch (e) {
        LogUtil.error('获取搜索建议服务错误: $e');
        // 使用ErrorHandler处理异常，但不显示snackbar，避免干扰用户输入
        ErrorHandler.handleException(
          e,
          message: '获取搜索建议失败',
          showSnackbar: false,
        );
        callback([]);
      }
    });
  }
  
  /// 获取随机角色名
  /// 
  /// 用于搜索框的默认提示
  Future<String> getRandomRoleName() async {
    try {
      return await _repository.getRandomRoleName();
    } catch (e) {
      LogUtil.error('获取随机角色名服务错误: $e');
      // 使用ErrorHandler处理异常，但不显示snackbar，因为这是非关键功能
      ErrorHandler.handleException(
        e,
        message: '获取随机角色名失败',
        showSnackbar: false,
      );
      return '';
    }
  }
  
  /// 获取搜索历史
  /// 
  /// 返回用户的搜索历史记录
  Future<List<SearchRecord>> getSearchHistory({
    int page = 1,
    int size = 10,
    bool forceRefresh = false,
  }) async {
    try {
      final result = await _repository.getSearchHistory(
        page: page,
        size: size,
        forceRefresh: forceRefresh,
      );
      
      if (result.containsKey('items') && result['items'] is List) {
        return List<SearchRecord>.from(result['items']);
      }
      
      return [];
    } catch (e) {
      LogUtil.error('获取搜索历史服务错误: $e');
      // 使用ErrorHandler处理异常
      ErrorHandler.handleException(
        e,
        message: '获取搜索历史失败',
        showSnackbar: true,
      );
      return [];
    }
  }
  
  /// 删除搜索记录
  /// 
  /// 删除指定的搜索记录
  Future<bool> deleteSearchRecord(int recordId) async {
    try {
      return await _repository.deleteSearchRecord(recordId);
    } catch (e) {
      LogUtil.error('删除搜索记录服务错误: $e');
      // 使用ErrorHandler处理异常
      ErrorHandler.handleException(
        e,
        message: '删除搜索记录失败',
        showSnackbar: true,
      );
      return false;
    }
  }
  

  
  /// 删除所有搜索记录
  /// 
  /// 通过获取所有搜索历史并批量删除实现
  Future<int> deleteAllSearchRecords() async {
    try {
      // 批量删除
      final deleteResult = await _repository.deleteAllSearchRecords();
      return deleteResult['deleted_count'] ?? 0;
    } catch (e) {
      LogUtil.error('删除所有搜索记录服务错误: $e');
      // 使用ErrorHandler处理异常
      ErrorHandler.handleException(
        e,
        message: '删除所有搜索记录失败',
        showSnackbar: true,
      );
      return 0;
    }
  }
  
  /// 加载搜索的下一页
  /// 
  /// 根据当前搜索条件加载下一页结果
  /// [currentResult] 当前搜索结果
  /// [keyword] 搜索关键词
  /// 返回新的搜索结果
  Future<Map<String, dynamic>> loadNextPage(
    Map<String, dynamic> currentResult, {
    required String keyword,
  }) async {
    try {
      final int currentPage = currentResult['page'] ?? 1;
      final int totalPages = currentResult['pages'] ?? 1;
      final int pageSize = currentResult['size'] ?? 20;
      
      // 检查是否有下一页
      if (currentPage >= totalPages) {
        LogUtil.debug('没有更多搜索结果了');
        return currentResult;
      }
      
      // 加载下一页
      final nextPage = currentPage + 1;
      LogUtil.debug('加载搜索结果下一页: $nextPage');
      
      final result = await searchRoles(
        keyword: keyword,
        page: nextPage,
        pageSize: pageSize,
      );
      
      // 合并结果
      if (result['items'] is List<AiRole> && currentResult['items'] is List<AiRole>) {
        final List<AiRole> combinedItems = [
          ...(currentResult['items'] as List<AiRole>),
          ...(result['items'] as List<AiRole>),
        ];
        
        return {
          ...result,
          'items': combinedItems,
        };
      }
      
      return result;
    } catch (e) {
      LogUtil.error('加载搜索下一页错误: $e');
      // 使用ErrorHandler处理异常
      ErrorHandler.handleException(
        e,
        message: '加载更多结果失败',
        showSnackbar: true,
      );
      return currentResult;
    }
  }
  
  /// 取消防抖计时器和当前搜索请求
  void cancelDebounce() {
    _debounceTimer?.cancel();
    _cancelCurrentSearch();
  }
} 