import 'package:get/get.dart';
import 'package:rolio/common/event/event_bus.dart';
import 'package:rolio/common/utils/logger.dart';
import 'package:rolio/manager/ws_message_manager.dart';
import 'package:rolio/modules/chat/model/message.dart';
import 'package:rolio/modules/chat/repository/chat_repository.dart';
import 'package:rolio/modules/chat/service/ai_channel_manager.dart';
import 'package:rolio/modules/chat/service/chat_manager.dart';
import 'package:rolio/modules/chat/service/message_service.dart';
import 'package:rolio/common/services/session_binding_service.dart';
import 'dart:async'; // Added for Timer

/// AI消息处理器
/// 
/// 负责处理来自不同AI角色的消息，并将其正确地分发到对应的会话
class AiMessageProcessor extends GetxService {
  // 依赖服务
  final ChatManager _chatManager = Get.find<ChatManager>();
  final MessageService _messageService = Get.find<MessageService>();
  final ChatRepository _repository = Get.find<ChatRepository>();
  final EventBus _eventBus = Get.find<EventBus>();
  late final AiChannelManager _channelManager;
  
  // 构造函数
  AiMessageProcessor() {
    // 检查AiChannelManager是否已注册，如果没有则注册
    if (!Get.isRegistered<AiChannelManager>()) {
      Get.put(AiChannelManager());
    }
    _channelManager = Get.find<AiChannelManager>();
  }
  
  // 标记事件监听器是否已设置
  bool _eventListenersSetup = false;

  // 消息去重缓存 - 仅用于处理期间去重，定期清理
  final Set<String> _processingMessageIds = <String>{};
  
  // 定时清理定时器
  Timer? _cleanupTimer;
  
  // 订阅管理
  final _subscriptions = CompositeSubscription();
  
  @override
  void onInit() {
    super.onInit();
    _setupEventListeners();
    
    // 设置定期清理消息缓存任务
    _setupCleanupTask();
  }
  
  /// 设置定期清理任务
  void _setupCleanupTask() {
    // 取消现有定时器
    _cleanupTimer?.cancel();
    
    // 创建新定时器 - 每30秒清理一次
    _cleanupTimer = Timer.periodic(const Duration(seconds: 30), (_) {
      _cleanupCache();
    });
    
    LogUtil.debug('已设置消息缓存定期清理任务，间隔30秒');
  }
  
  /// 清理缓存
  void _cleanupCache() {
    try {
      // 清空消息去重缓存
      _processingMessageIds.clear();
      LogUtil.debug('已清空消息处理去重缓存');
    } catch (e) {
      LogUtil.error('清理缓存失败: $e');
    }
  }
  
  /// 设置事件监听器
  void _setupEventListeners() {
    // 防止重复设置事件监听器
    if (_eventListenersSetup) {
      LogUtil.debug('AiMessageProcessor - 事件监听器已设置，跳过重复设置');
      return;
    }

    LogUtil.debug('AiMessageProcessor - 设置事件监听器');
    
    // 监听统一的角色消息事件
    _subscriptions.add(_eventBus.on(AppEvent.roleMessage).listen((event) {
      final data = event['data'];
      if (data is Map) {
        // 检查是否是原始WebSocket消息，只处理原始消息
        final isOriginalMessage = data['isOriginalMessage'] as bool? ?? false;
        if (!isOriginalMessage) {
          LogUtil.debug('AiMessageProcessor - 忽略非原始消息，避免重复处理');
          return;
        }
        
        final roleId = data['roleId'] as int?;
        final message = data['message'] as WsMsg?;
        final isActiveRole = data['isActiveRole'] as bool? ?? false;
        
        if (roleId != null && message != null) {
          LogUtil.debug('收到角色消息事件: roleId=$roleId, isActiveRole=$isActiveRole');
          
          // 消息去重处理 - 仅在处理期间去重
          final messageKey = '${roleId}_${message.data.hashCode}_${DateTime.now().millisecondsSinceEpoch}';
          if (_processingMessageIds.contains(messageKey)) {
            LogUtil.debug('消息处理中，跳过重复处理: roleId=$roleId, key=$messageKey');
            return;
          }
          
          // 添加到处理中集合 - 仅在处理期间保持
          _processingMessageIds.add(messageKey);
          
          // 只处理非活跃角色的消息
          if (!isActiveRole) {
            _processNonActiveRoleMessage(roleId, message);
          }
          
          // 处理完成后立即清理，但保留短时间用于去重
          Timer(const Duration(seconds: 5), () {
            _processingMessageIds.remove(messageKey);
          });
        }
      }
    }));
    
    // 保留对原有非活跃角色消息事件的监听，确保向后兼容
    _subscriptions.add(_eventBus.on(AppEvent.nonActiveRoleMessage).listen((event) {
      final data = event['data'];
      if (data is Map) {
        final roleId = data['roleId'] as int?;
        final message = data['message'] as WsMsg?;
        
        if (roleId != null && message != null) {
          LogUtil.debug('收到传统非活跃角色消息事件: roleId=$roleId');
          _processNonActiveRoleMessage(roleId, message);
        }
      }
    }));
    
    // 监听角色会话绑定事件
    _subscriptions.add(_eventBus.on(AppEvent.roleConversationBound).listen((event) {
      final data = event['data'];
      if (data is Map && data.containsKey('role_id') && data.containsKey('conversation_id')) {
        final roleId = data['role_id'] as int;
        final conversationId = data['conversation_id'] as int;
        
        // 确保订阅该角色
        _channelManager.subscribeToRole(roleId);
      }
    }));
    
    // 标记事件监听器已设置
    _eventListenersSetup = true;
    LogUtil.debug('AiMessageProcessor - 事件监听器设置完成');
  }
  
  /// 处理非活跃角色的消息
  /// 
  /// [roleId] 角色ID
  /// [wsMsg] WebSocket消息
  void _processNonActiveRoleMessage(int roleId, WsMsg wsMsg) {
    try {
      LogUtil.debug('处理非活跃角色消息: roleId=$roleId');
      
      // 提取消息数据
      final extractedData = _repository.extractAiMessageData(wsMsg.data, roleId);
      final messageData = extractedData['messageData'];
      final receivedRoleId = extractedData['receivedRoleId'] as int?;
      final receivedConversationId = extractedData['receivedConversationId'] as int?;
      
      if (messageData == null) {
        LogUtil.warn('无法提取非活跃角色的消息数据');
        return;
      }
      
      // 获取会话ID
      final useConversationId = receivedConversationId ?? _channelManager.getConversationIdForRole(roleId) ?? 0;
      
      // 如果有会话ID，直接转发消息数据
      if (useConversationId > 0) {
        // 准备消息数据直接传递给MessageService处理
        if (messageData is Map<String, dynamic>) {
          // 确保消息数据中包含必要的字段
          messageData['conversation_id'] = useConversationId;
          messageData['sender'] = 'assistant';
          messageData['event'] = 'ai_reply';
          
          // 直接将消息数据传递给MessageService处理
          _messageService.processAiMessage(messageData);
          
          LogUtil.debug('已将消息数据直接转发: roleId=$roleId, conversationId=$useConversationId');
        } else {
          LogUtil.warn('消息数据格式不正确，无法直接转发');
        }
        
        // 更新角色会话绑定
        _channelManager.bindRoleToConversation(roleId, useConversationId);
        
        LogUtil.info('已处理非活跃角色消息: roleId=$roleId, conversationId=$useConversationId');
      } else {
        LogUtil.warn('消息没有有效的会话ID，无法处理: roleId=$roleId');
      }
    } catch (e) {
      LogUtil.error('处理非活跃角色消息失败: $e');
    }
  }
  
  /// 订阅指定角色
  /// 
  /// [roleId] 角色ID
  /// 返回是否成功订阅
  bool subscribeToRole(int roleId) {
    return _channelManager.subscribeToRole(roleId);
  }
  
  /// 订阅多个角色
  /// 
  /// [roleIds] 角色ID列表
  void subscribeToMultipleRoles(List<int> roleIds) {
    _channelManager.subscribeToMultipleRoles(roleIds);
  }
  
  /// 取消订阅角色
  /// 
  /// [roleId] 角色ID
  void unsubscribeFromRole(int roleId) {
    _channelManager.unsubscribeFromRole(roleId);
  }
  
  /// 清除所有缓存
  void clearAllCache() {
    _processingMessageIds.clear();
    _channelManager.clearAllCache();
    LogUtil.debug('已清除所有消息缓存');
  }
  
  /// 获取所有已订阅的角色ID
  /// 
  /// 返回已订阅的角色ID列表
  List<int> getSubscribedRoleIds() {
    return _channelManager.getSubscribedRoleIds();
  }
  
  /// 获取角色的会话ID
  /// 
  /// [roleId] 角色ID
  /// 返回会话ID，如果没有则返回null
  int? getConversationIdForRole(int roleId) {
    return _channelManager.getConversationIdForRole(roleId);
  }
  
  @override
  void onClose() {
    // 取消定时器
    _cleanupTimer?.cancel();
    
    // 取消所有事件订阅
    _subscriptions.cancel();
    
    // 清理所有缓存
    clearAllCache();
    
    LogUtil.debug('AiMessageProcessor - onClose() - 资源已释放');
    
    super.onClose();
  }
}

/// 复合订阅
/// 用于管理多个StreamSubscription
class CompositeSubscription {
  final List<StreamSubscription> _subscriptions = [];

  void add(StreamSubscription subscription) {
    _subscriptions.add(subscription);
  }

  Future<void> cancel() async {
    for (final subscription in _subscriptions) {
      await subscription.cancel();
    }
    _subscriptions.clear();
  }

  void pause([Future<void>? resumeSignal]) {
    for (final subscription in _subscriptions) {
      subscription.pause(resumeSignal);
    }
  }

  void resume() {
    for (final subscription in _subscriptions) {
      subscription.resume();
    }
  }

  bool get isPaused =>
      _subscriptions.isEmpty ? false : _subscriptions.first.isPaused;
} 