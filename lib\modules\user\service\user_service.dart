import 'package:get/get.dart';
import 'package:rolio/common/utils/logger.dart';
import 'package:rolio/manager/global_state.dart';
import 'package:rolio/modules/user/repository/user_repository.dart';
import 'package:rolio/common/event/event_bus.dart';
import 'package:rolio/common/models/user.dart' as app;
import 'package:firebase_auth/firebase_auth.dart' as firebase_auth;

/// 用户头像服务
/// 负责管理用户头像的相关操作
class UserAvatarService extends GetxService {
  // 用户头像仓库
  final UserAvatarRepository _repository;
  
  // Firebase Auth实例
  final firebase_auth.FirebaseAuth _auth = firebase_auth.FirebaseAuth.instance;
  
  // 全局状态
  final GlobalState _globalState = Get.find<GlobalState>();
  
  // 事件总线
  final EventBus _eventBus = Get.find<EventBus>();
  
  // 构造函数
  UserAvatarService({required UserAvatarRepository repository}) : _repository = repository;
  
  // 当前用户的Rx引用，保持与全局状态同步
  final Rx<app.User?> currentUser = Rx<app.User?>(null);
  
  @override
  void onInit() {
    super.onInit();
    // 从全局状态同步用户信息
    _syncUserFromGlobalState();
  }
  
  /// 从全局状态同步用户信息
  void _syncUserFromGlobalState() {
    // 初始化时，从全局状态获取用户
    currentUser.value = _globalState.currentUser.value;
    
    // 监听全局状态用户变化
    _globalState.currentUser.listen((user) {
      if (user != currentUser.value) {
        currentUser.value = user;
        LogUtil.debug('从全局状态同步用户信息: ${user?.uid ?? 'null'}');
      }
    });
  }

  /// 更新用户显示名称
  Future<bool> updateDisplayName(String displayName) async {
    try {
      // 验证用户名
      if (displayName.trim().isEmpty) {
        LogUtil.warn('用户名不能为空');
        return false;
      }
      
      // 调用仓库更新用户名
      final success = await _repository.updateDisplayName(displayName);
      
      if (success) {
        // 从Firebase获取更新后的用户信息
        final firebaseUser = _auth.currentUser;
        
        if (firebaseUser != null) {
          // 创建更新后的用户对象
          final updatedUser = app.User.fromFirebaseUser(firebaseUser);
          
          // 更新全局状态
          _globalState.setCurrentUser(updatedUser);
          
          // 更新本地状态
          currentUser.value = updatedUser;
          
          // 触发用户信息更新事件
          _eventBus.fire('user_profile_updated', {
            'userId': updatedUser.uid,
            'field': 'displayName',
            'value': displayName
          });
          
          LogUtil.info('用户名更新成功，全局状态已更新');
        }
      }
      
      return success;
    } catch (e) {
      LogUtil.error('用户名更新服务失败: $e');
      return false;
    }
  }
  
  /// 更新用户头像
  Future<bool> updatePhotoURL(String photoURL) async {
    try {
      // 调用仓库更新头像
      final success = await _repository.updatePhotoURL(photoURL);
      
      if (success) {
        // 从Firebase获取更新后的用户信息
        final firebaseUser = _auth.currentUser;
        
        if (firebaseUser != null) {
          // 创建更新后的用户对象
          final updatedUser = app.User.fromFirebaseUser(firebaseUser);
          
          // 更新全局状态
          _globalState.setCurrentUser(updatedUser);
          
          // 更新本地状态
          currentUser.value = updatedUser;
          
          // 触发用户信息更新事件
          _eventBus.fire('user_profile_updated', {
            'userId': updatedUser.uid,
            'field': 'photoURL',
            'value': photoURL
          });
          
          LogUtil.info('用户头像更新成功，全局状态已更新');
        }
      }
      
      return success;
    } catch (e) {
      LogUtil.error('用户头像服务更新头像失败: $e');
      return false;
    }
  }
  
  /// 获取所有可用头像URL列表
  List<String> getAvatarUrls() {
    return _repository.getAvatarUrls();
  }
  
  /// 获取默认头像URL
  String getDefaultAvatarUrl() {
    return _repository.defaultAvatarUrl;
  }
} 