import 'package:get/get.dart';
import 'package:rolio/common/models/ai_role.dart';
import 'package:rolio/common/utils/logger.dart';
import 'package:rolio/common/utils/error_handler.dart';
import 'package:rolio/modules/role/service/role_service.dart';
import 'package:rolio/common/utils/image_preloader.dart';
import 'package:rolio/common/utils/toast_util.dart';
import 'package:rolio/common/event/event_bus.dart';  // 导入事件总线
import 'dart:async';  // 导入用于StreamSubscription

/// 角色控制器
/// 负责管理角色详情页的UI状态和业务逻辑
class RoleController extends GetxController {
  // 服务
  late final RoleService roleService;
  
  // 当前角色
  final Rx<AiRole?> currentRole = Rx<AiRole?>(null);
  
  // 加载状态
  final RxBool isLoading = true.obs;
  
  // 收藏操作状态
  final RxBool isFavoriteLoading = false.obs;
  
  // 错误状态
  final RxBool hasError = false.obs;
  final RxString errorMessage = ''.obs;
  
  // 图片预加载状态
  final RxBool isCoverPreloaded = false.obs;
  
  // 图片预加载器
  final ImagePreloader _imagePreloader = ImagePreloader();
  
  // 事件订阅管理
  StreamSubscription? _favoriteStatusSubscription;
  
  // 重试次数
  int _retryCount = 0;
  static const int MAX_RETRY = 3;
  
  @override
  void onInit() {
    super.onInit();
    
    // 获取服务实例
    roleService = Get.find<RoleService>();
    
    // 监听角色收藏状态变更事件
    _setupFavoriteStatusListener();
    
    // 获取路由参数
    final arguments = Get.arguments;
    if (arguments != null && arguments is Map<String, dynamic>) {
      final roleId = arguments['roleId'];
      if (roleId != null && roleId is int) {
        // 获取角色详情（包含收藏信息）
        getRoleDetail(roleId);
      } else {
        LogUtil.error('角色ID无效: $roleId');
        _setError('无效的角色ID');
      }
    } else {
      LogUtil.error('未提供角色ID参数');
      _setError('未提供角色ID');
    }
  }
  
  /// 设置收藏状态监听器
  void _setupFavoriteStatusListener() {
    _favoriteStatusSubscription = EventBus().on(AppEvent.roleFavoriteStatusChanged).listen((event) {
      if (event != null && event is Map<String, dynamic>) {
        final roleId = event['data']['role_id'];
        final isFavorited = event['data']['is_favorited'];
        
        LogUtil.debug('RoleController: 收到角色收藏状态变更事件: roleId=$roleId, isFavorited=$isFavorited');
        
        // 如果是当前角色，更新UI状态
        if (roleId != null && currentRole.value != null && currentRole.value!.id == roleId) {
          currentRole.value = currentRole.value!.copyWith(
            isFavorited: isFavorited,
            favoritedAt: isFavorited ? DateTime.now() : null,
          );
          LogUtil.debug('RoleController: 已更新当前角色收藏状态: $isFavorited');
        }
      }
    });
  }

  /// 打开搜索页面
  void goToSearch() {
    LogUtil.debug('打开搜索页面');
    Get.toNamed('/role/search');
  }

  /// 获取角色详情
  /// 
  /// 通过RoleService获取角色详细信息
  /// [roleId] 角色ID
  /// [forceRefresh] 是否强制刷新，不使用缓存
  Future<void> getRoleDetail(int roleId, {bool forceRefresh = false}) async {
    try {
      isLoading.value = true;
      hasError.value = false;
      
      LogUtil.info('获取角色详情: ID=$roleId, forceRefresh=$forceRefresh');
      
      // 从RoleService获取完整角色详情
      final role = await roleService.getRoleDetail(roleId, forceRefresh: forceRefresh);
      
      if (role != null) {
        // 更新当前角色
        currentRole.value = role;
        _retryCount = 0; // 成功后重置重试次数
        LogUtil.debug('成功获取角色详情: ${role.name}, 收藏状态: ${role.isFavorited}');
        
        // 检查封面图是否已预加载
        _checkAndPreloadCoverImage(role);
      } else {
        LogUtil.error('未找到角色: ID=$roleId');
        _setError('Role not found');
      }
    } catch (e) {
      LogUtil.error('获取角色详情失败: $e');
      _setError('Failed to load role details');
      
      // 自动重试逻辑 - 仅在非强制刷新且重试次数未超过上限时
      if (!forceRefresh && _retryCount < MAX_RETRY) {
        _retryCount++;
        LogUtil.info('自动重试获取角色详情，第$_retryCount次: ID=$roleId');
        
        // 延迟1秒后重试
        Future.delayed(const Duration(seconds: 1), () {
          getRoleDetail(roleId, forceRefresh: true);
        });
      } else {
        ErrorHandler.handleException(e);
      }
    } finally {
      isLoading.value = false;
    }
  }
  
  /// 重试获取角色详情
  Future<void> retryGetRoleDetail() async {
    final role = currentRole.value;
    if (role != null) {
      // 重试获取角色详情
      _retryCount++;
      LogUtil.info('重试获取角色详情，第$_retryCount次: ID=${role.id}');
      await getRoleDetail(role.id, forceRefresh: true);
    } else {
      // 从路由参数获取角色ID
      final arguments = Get.arguments;
      if (arguments != null && arguments is Map<String, dynamic>) {
        final roleId = arguments['roleId'];
        if (roleId != null && roleId is int) {
          _retryCount++;
          LogUtil.info('从路由参数重试获取角色详情，第$_retryCount次: ID=$roleId');
          await getRoleDetail(roleId, forceRefresh: true);
        } else {
          // 如果没有角色ID，无法重试
          LogUtil.error('无法重试，当前没有角色ID');
          ToastUtil.error('Cannot retry, no role ID available');
        }
      } else {
        // 如果没有角色ID，无法重试
        LogUtil.error('无法重试，当前没有角色ID');
        ToastUtil.error('Cannot retry, no role ID available');
      }
    }
  }

  /// 切换收藏状态
  ///
  /// 如果角色已收藏则取消收藏，否则收藏角色
  Future<void> toggleFavorite() async {
    try {
      final role = currentRole.value;
      if (role == null) {
        LogUtil.warn('当前没有角色，无法切换收藏状态');
        return;
      }
      
      isFavoriteLoading.value = true;
      LogUtil.debug('切换收藏状态，当前状态: ${role.isFavorited}, 角色ID: ${role.id}');
      
      // 保存原始状态，用于错误回滚
      final originalStatus = role.isFavorited;
      
      // 乐观更新UI，立即反馈给用户
      final updatedRole = role.copyWith(
        isFavorited: !originalStatus,
        favoritedAt: !originalStatus ? DateTime.now() : null, // 如果是收藏操作则添加时间，否则清空
      );
      
      // 更新UI状态
      currentRole.value = updatedRole;
      
      // 发送请求到服务器
      final result = await roleService.toggleFavorite(role.id);
      
      // 验证服务器响应
      if (result == null) {
        // 服务器操作失败，回滚UI状态
        LogUtil.warn('切换收藏状态失败，回滚UI状态');
        currentRole.value = role.copyWith(
          isFavorited: originalStatus,
          favoritedAt: originalStatus ? role.favoritedAt : null,
        );
        ToastUtil.error('Failed to change favorite status');
      } else if (result != !originalStatus) {
        // 服务器状态与预期不一致，同步为服务器状态
        LogUtil.warn('服务器返回状态与预期不一致，同步为服务器状态');
        currentRole.value = role.copyWith(
          isFavorited: result,
          favoritedAt: result ? DateTime.now() : null,
        );
      }
      
      // 发送收藏状态变更事件 - 无论成功失败都发送，确保状态一致性
      EventBus().fire(AppEvent.roleFavoriteStatusChanged, {
        'role_id': role.id,
        'is_favorited': result ?? originalStatus,
      });
    } catch (e) {
      LogUtil.error('切换收藏状态异常: $e');
      
      // 发生异常时，恢复到操作前的状态
      final role = currentRole.value;
      if (role != null) {
        // 获取角色的真实状态
        try {
          final freshRole = await roleService.getRoleDetail(role.id);
          if (freshRole != null) {
            currentRole.value = freshRole;
            
            // 发送收藏状态变更事件
            EventBus().fire(AppEvent.roleFavoriteStatusChanged, {
              'role_id': role.id,
              'is_favorited': freshRole.isFavorited,
            });
          }
        } catch (_) {
          // 如果刷新失败，只能使用之前的状态
          ToastUtil.error('An error occurred while updating favorite status');
        }
      }
    } finally {
      isFavoriteLoading.value = false;
    }
  }
  
  /// 检查并预加载封面图
  void _checkAndPreloadCoverImage(AiRole role) {
    if (role.coverUrl.isEmpty) {
      isCoverPreloaded.value = true;
      return;
    }
    
    // 检查图片是否已预加载
    isCoverPreloaded.value = _imagePreloader.isImagePreloaded(role.coverUrl);
    
    if (!isCoverPreloaded.value) {
      LogUtil.debug('封面图未预加载，开始预加载: ${role.coverUrl}');
      
      // 高优先级预加载封面图
      _imagePreloader.preloadImage(
        role.coverUrl,
        priority: ImagePreloadPriority.high,
        onComplete: (success) {
          LogUtil.debug('封面图预加载${success ? "成功" : "失败"}: ${role.coverUrl}');
          isCoverPreloaded.value = success;
        }
      );
    } else {
      LogUtil.debug('封面图已预加载: ${role.coverUrl}');
    }
    
    // 预加载头像图片
    if (role.avatarUrl.isNotEmpty && !_imagePreloader.isImagePreloaded(role.avatarUrl)) {
      _imagePreloader.preloadImage(
        role.avatarUrl,
        priority: ImagePreloadPriority.medium
      );
    }
  }
  
  /// 设置错误状态
  void _setError(String message) {
    hasError.value = true;
    errorMessage.value = message;
  }
  
  @override
  void onClose() {
    // 取消事件订阅
    _favoriteStatusSubscription?.cancel();
    
    // 清理图片预加载器资源
    if (currentRole.value != null) {
      final role = currentRole.value!;
      if (role.coverUrl.isNotEmpty) {
        // 从预加载队列中移除封面图
        LogUtil.debug('清理封面图预加载资源: ${role.coverUrl}');
        _imagePreloader.clearMemoryCache();
      }
    }
    
    // 释放其他资源
    _retryCount = 0;
    currentRole.value = null;
    
    LogUtil.debug('RoleController: 已释放资源');
    super.onClose();
  }
}
